@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/tokens/m2/mat/icon' as tokens-mat-icon;
@use '../core/tokens/token-utils';
@use '../core/style/sass-utils';

@mixin _palette-colors($theme, $palette-name) {
  $color: inspection.get-theme-color($theme, $palette-name, text);
  $tokens: tokens-mat-icon.private-get-icon-color-tokens($color);
  @include token-utils.create-token-values(tokens-mat-icon.$prefix, $tokens);
}

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-icon.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  } @else {
  }
}

/// Outputs color theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the icon: primary, secondary, tertiary, or error
///     (If not specified, default neutral color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mat-icon.$prefix,
        tokens-mat-icon.get-color-tokens($theme)
      );
    }

    .mat-icon {
      &.mat-primary {
        @include _palette-colors($theme, primary);
      }

      &.mat-accent {
        @include _palette-colors($theme, accent);
      }

      &.mat-warn {
        @include _palette-colors($theme, warn);
      }
    }
  }
}

/// Outputs typography theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  } @else {
  }
}

/// Outputs density theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  } @else {
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: tokens-mat-icon.$prefix,
      tokens: tokens-mat-icon.get-token-slots(),
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values($tokens, _define-overrides()...);
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the icon: surface, primary, secondary, tertiary,
///     or error (If not specified, default surface color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-icon') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
    'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector'
  );
  $mat-icon-tokens: token-utils.get-tokens-for($tokens, tokens-mat-icon.$prefix, $options...);
  @include token-utils.create-token-values(tokens-mat-icon.$prefix, $mat-icon-tokens);
}
