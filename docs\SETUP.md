# Development Setup Guide

This comprehensive guide will walk you through setting up the Any2PDF development environment from scratch, creating the exact project structure defined in our architecture document.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Project Initialization](#project-initialization)
3. [Frontend Setup (Angular)](#frontend-setup-angular)
4. [Backend Setup (FastAPI)](#backend-setup-fastapi)
5. [Docker Configuration](#docker-configuration)
6. [Documentation Setup](#documentation-setup)
7. [Development Scripts](#development-scripts)
8. [GitHub Workflows](#github-workflows)
9. [Environment Configuration](#environment-configuration)
10. [Verification](#verification)

## Prerequisites

Before starting, ensure you have the following installed:

```bash
# Check versions
node --version    # Should be 20.16.0+
npm --version     # Should be 10.9.0+
python --version  # Should be 3.12+
git --version     # Any recent version
docker --version  # Optional, for containerization
```

### Required Software
- **Node.js 20.16.0+** and **npm 10.9.0+**
- **Python 3.12+**
- **Angular CLI 19.2.8+**
- **Git**
- **Docker** (optional, for containerized development)

### Install Angular CLI
```bash
npm install -g @angular/cli@19
ng version  # Verify installation
```

## Project Initialization

### 1. Create Root Directory
```bash
# Create and navigate to project root
mkdir Any2PDF
cd Any2PDF

# Initialize git repository
git init
```

### 2. Create Basic Structure
```bash
# Create main directories
mkdir frontend backend docker docs scripts .github
mkdir .github/workflows
```

## Frontend Setup (Angular)

### 1. Generate Angular Application
```bash
# Navigate to project root
cd Any2PDF

# Create Angular application with specific configuration
ng new frontend --routing=true --style=css --skip-git=true --package-manager=npm
cd frontend
```

### 2. Install Dependencies
```bash
# Install Angular Material
ng add @angular/material --theme=indigo-pink --typography=true --animations=true

# Install Tailwind CSS
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p

# Install additional dependencies
npm install @angular/cdk
npm install rxjs
```

### 3. Configure Tailwind CSS
```bash
# Update tailwind.config.js
cat > tailwind.config.js << 'EOF'
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
EOF

# Update styles.css
cat > src/styles.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Angular Material theme imports */
@import '@angular/material/prebuilt-themes/indigo-pink.css';
EOF
```

### 4. Create Component Structure
```bash
# Navigate to src/app
cd src/app

# Create components directory and components
ng generate component components/file-upload --skip-tests=false
ng generate component components/conversion-panel --skip-tests=false
ng generate component components/progress-bar --skip-tests=false
ng generate component components/download-manager --skip-tests=false

# Create services
ng generate service services/api --skip-tests=false
ng generate service services/file --skip-tests=false
ng generate service services/conversion --skip-tests=false

# Create models directory and interfaces
mkdir models
touch models/file.model.ts
touch models/conversion.model.ts
touch models/api-response.model.ts

# Create pages
ng generate component pages/home --skip-tests=false
ng generate component pages/convert --skip-tests=false
ng generate component pages/about --skip-tests=false

# Create additional directories
mkdir guards interceptors pipes
ng generate guard guards/auth --skip-tests=false
ng generate interceptor interceptors/api --skip-tests=false
ng generate pipe pipes/file-size --skip-tests=false
```

### 5. Create Assets Structure
```bash
# Create assets subdirectories
cd ../assets
mkdir images icons styles

# Create placeholder files
touch images/.gitkeep
touch icons/.gitkeep
touch styles/.gitkeep
```

### 6. Configure Environment Files
```bash
cd ../environments

# Update environment.ts
cat > environment.ts << 'EOF'
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8000',
  maxFileSize: 52428800, // 50MB
};
EOF

# Update environment.prod.ts
cat > environment.prod.ts << 'EOF'
export const environment = {
  production: true,
  apiUrl: '/api',
  maxFileSize: 52428800, // 50MB
};
EOF
```

### 7. Create Frontend Dockerfile
```bash
cd ../../  # Back to frontend root

cat > Dockerfile << 'EOF'
# Build stage
FROM node:20-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine
COPY --from=build /app/dist/frontend /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
EOF
```

## Backend Setup (FastAPI)

### 1. Create Backend Structure
```bash
# Navigate to backend directory
cd ../backend

# Create Python virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
# source venv/bin/activate

# Create directory structure
mkdir -p app/api/endpoints
mkdir -p app/core
mkdir -p app/services
mkdir -p app/models
mkdir -p app/schemas
mkdir -p app/utils
mkdir -p tests/test_api
mkdir -p tests/test_services
mkdir -p tests/test_utils
mkdir -p alembic/versions
```

### 2. Create Python Files
```bash
# Create __init__.py files
touch app/__init__.py
touch app/api/__init__.py
touch app/api/endpoints/__init__.py
touch app/core/__init__.py
touch app/services/__init__.py
touch app/models/__init__.py
touch app/schemas/__init__.py
touch app/utils/__init__.py
touch tests/__init__.py
```

### 3. Create Main Application Files
```bash
# Create main.py
cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.endpoints import conversion, files, health
from app.core.config import settings

app = FastAPI(
    title="Any2PDF API",
    description="File conversion API for Any2PDF application",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(files.router, prefix="/files", tags=["files"])
app.include_router(conversion.router, prefix="/convert", tags=["conversion"])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF
```

### 4. Create Core Configuration
```bash
# Create config.py
cat > app/core/config.py << 'EOF'
from pydantic_settings import BaseSettings
from typing import List

class Settings(BaseSettings):
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    ENVIRONMENT: str = "development"
    MAX_FILE_SIZE: int = 52428800  # 50MB
    UPLOAD_DIR: str = "./uploads"
    CLEANUP_INTERVAL: int = 3600  # 1 hour
    CORS_ORIGINS: List[str] = ["http://localhost:4200"]
    
    # Database
    DATABASE_URL: str = "sqlite:///./any2pdf.db"
    
    class Config:
        env_file = ".env"

settings = Settings()
EOF

# Create database.py
cat > app/core/database.py << 'EOF'
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
EOF

# Create security.py
cat > app/core/security.py << 'EOF'
import hashlib
import secrets
from typing import Optional

def generate_file_hash(content: bytes) -> str:
    """Generate SHA256 hash of file content."""
    return hashlib.sha256(content).hexdigest()

def generate_secure_filename() -> str:
    """Generate a secure random filename."""
    return secrets.token_urlsafe(16)

def validate_file_type(filename: str, allowed_types: list) -> bool:
    """Validate file type based on extension."""
    extension = filename.lower().split('.')[-1]
    return extension in allowed_types
EOF
```

### 5. Create API Endpoints
```bash
# Create deps.py
cat > app/api/deps.py << 'EOF'
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db

def get_database() -> Session:
    """Dependency to get database session."""
    return Depends(get_db)
EOF

# Create health endpoint
cat > app/api/endpoints/health.py << 'EOF'
from fastapi import APIRouter

router = APIRouter()

@router.get("/")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "Any2PDF API"}
EOF

# Create files endpoint
cat > app/api/endpoints/files.py << 'EOF'
from fastapi import APIRouter, UploadFile, File, HTTPException
from app.services.file_service import FileService

router = APIRouter()
file_service = FileService()

@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a file for conversion."""
    try:
        result = await file_service.save_uploaded_file(file)
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
EOF

# Create conversion endpoint
cat > app/api/endpoints/conversion.py << 'EOF'
from fastapi import APIRouter, HTTPException
from app.services.pdf_service import PDFService
from app.services.image_service import ImageService

router = APIRouter()
pdf_service = PDFService()
image_service = ImageService()

@router.post("/jpg-to-pdf")
async def convert_jpg_to_pdf(file_id: str):
    """Convert JPG to PDF."""
    try:
        result = await pdf_service.jpg_to_pdf(file_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/pdf-to-jpg")
async def convert_pdf_to_jpg(file_id: str):
    """Convert PDF to JPG."""
    try:
        result = await image_service.pdf_to_jpg(file_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
EOF

### 6. Create Service Files
```bash
# Create file_service.py
cat > app/services/file_service.py << 'EOF'
import os
import uuid
from fastapi import UploadFile
from app.core.config import settings
from app.core.security import generate_secure_filename, validate_file_type

class FileService:
    def __init__(self):
        self.upload_dir = settings.UPLOAD_DIR
        os.makedirs(self.upload_dir, exist_ok=True)

    async def save_uploaded_file(self, file: UploadFile) -> dict:
        """Save uploaded file and return file info."""
        # Validate file type
        allowed_types = ['jpg', 'jpeg', 'pdf', 'png']
        if not validate_file_type(file.filename, allowed_types):
            raise ValueError("Unsupported file type")

        # Generate secure filename
        file_id = str(uuid.uuid4())
        extension = file.filename.split('.')[-1].lower()
        secure_filename = f"{file_id}.{extension}"
        file_path = os.path.join(self.upload_dir, secure_filename)

        # Save file
        content = await file.read()
        with open(file_path, "wb") as f:
            f.write(content)

        return {
            "file_id": file_id,
            "filename": secure_filename,
            "original_name": file.filename,
            "size": len(content),
            "path": file_path
        }
EOF

# Create pdf_service.py
cat > app/services/pdf_service.py << 'EOF'
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from PIL import Image
import os

class PDFService:
    async def jpg_to_pdf(self, file_id: str) -> dict:
        """Convert JPG to PDF."""
        # Implementation placeholder
        return {"status": "success", "message": "JPG to PDF conversion completed"}
EOF

# Create image_service.py
cat > app/services/image_service.py << 'EOF'
from PIL import Image
import fitz  # PyMuPDF
import os

class ImageService:
    async def pdf_to_jpg(self, file_id: str) -> dict:
        """Convert PDF to JPG."""
        # Implementation placeholder
        return {"status": "success", "message": "PDF to JPG conversion completed"}
EOF
```

### 7. Create Models and Schemas
```bash
# Create Pydantic models
cat > app/models/file.py << 'EOF'
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class FileBase(BaseModel):
    filename: str
    original_name: str
    size: int

class FileCreate(FileBase):
    pass

class File(FileBase):
    id: str
    created_at: datetime

    class Config:
        from_attributes = True
EOF

cat > app/models/conversion.py << 'EOF'
from pydantic import BaseModel
from datetime import datetime
from typing import Optional
from enum import Enum

class ConversionStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class ConversionBase(BaseModel):
    input_format: str
    output_format: str

class ConversionCreate(ConversionBase):
    file_id: str

class Conversion(ConversionBase):
    id: str
    file_id: str
    status: ConversionStatus
    created_at: datetime
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True
EOF

# Create database schemas
cat > app/schemas/conversion.py << 'EOF'
from sqlalchemy import Column, String, DateTime, Enum, Integer
from sqlalchemy.sql import func
from app.core.database import Base
from app.models.conversion import ConversionStatus

class ConversionDB(Base):
    __tablename__ = "conversions"

    id = Column(String, primary_key=True, index=True)
    file_id = Column(String, index=True)
    input_format = Column(String)
    output_format = Column(String)
    status = Column(Enum(ConversionStatus), default=ConversionStatus.PENDING)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
EOF
```

### 8. Create Utility Functions
```bash
# Create file_utils.py
cat > app/utils/file_utils.py << 'EOF'
import os
import time
from typing import List
from app.core.config import settings

def cleanup_old_files() -> int:
    """Clean up files older than cleanup interval."""
    upload_dir = settings.UPLOAD_DIR
    cleanup_time = time.time() - settings.CLEANUP_INTERVAL
    cleaned_count = 0

    for filename in os.listdir(upload_dir):
        file_path = os.path.join(upload_dir, filename)
        if os.path.getctime(file_path) < cleanup_time:
            os.remove(file_path)
            cleaned_count += 1

    return cleaned_count

def get_file_extension(filename: str) -> str:
    """Get file extension from filename."""
    return filename.split('.')[-1].lower()
EOF

# Create validation.py
cat > app/utils/validation.py << 'EOF'
from typing import List
from app.core.config import settings

def validate_file_size(file_size: int) -> bool:
    """Validate file size against maximum allowed."""
    return file_size <= settings.MAX_FILE_SIZE

def validate_conversion_pair(input_format: str, output_format: str) -> bool:
    """Validate if conversion between formats is supported."""
    supported_conversions = {
        'jpg': ['pdf'],
        'jpeg': ['pdf'],
        'pdf': ['jpg', 'jpeg', 'png'],
        'png': ['pdf']
    }

    return output_format in supported_conversions.get(input_format, [])
EOF

### 9. Create Requirements File
```bash
cat > requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
pydantic==2.5.0
pydantic-settings==2.1.0
sqlalchemy==2.0.23
alembic==1.13.1
reportlab==4.0.7
pillow==10.1.0
PyPDF2==3.0.1
PyMuPDF==1.23.8
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
EOF

# Install dependencies
pip install -r requirements.txt
```

### 10. Create Environment Template
```bash
cat > .env.example << 'EOF'
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
ENVIRONMENT=development

# File Configuration
MAX_FILE_SIZE=52428800
UPLOAD_DIR=./uploads
CLEANUP_INTERVAL=3600

# CORS Configuration
CORS_ORIGINS=["http://localhost:4200"]

# Database Configuration
DATABASE_URL=sqlite:///./any2pdf.db
EOF

# Copy to actual .env file
cp .env.example .env
```

### 11. Setup Alembic for Database Migrations
```bash
# Initialize Alembic
alembic init alembic

# Update alembic.ini
sed -i 's|sqlalchemy.url = driver://user:pass@localhost/dbname|sqlalchemy.url = sqlite:///./any2pdf.db|' alembic.ini

# Update alembic/env.py to include our models
cat > alembic/env.py << 'EOF'
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
from app.core.database import Base
from app.schemas.conversion import ConversionDB

config = context.config
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = Base.metadata

def run_migrations_offline() -> None:
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
EOF
```

### 12. Create Backend Dockerfile
```bash
cat > Dockerfile << 'EOF'
FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create uploads directory
RUN mkdir -p uploads

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF
```

## Docker Configuration

### 1. Create Docker Compose Files
```bash
# Navigate to docker directory
cd ../docker

# Create development docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    ports:
      - "4200:80"
    depends_on:
      - backend
    environment:
      - NODE_ENV=development

  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    environment:
      - DATABASE_URL=*******************************************/any2pdf
    volumes:
      - ../backend:/app
      - uploads:/app/uploads

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=any2pdf
      - POSTGRES_USER=any2pdf
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
  uploads:
EOF

### 2. Create Production Configuration and Scripts
```bash
# Create production docker-compose.prod.yml
cat > docker-compose.prod.yml << 'EOF'
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend

  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production

  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=postgresql://any2pdf:${DB_PASSWORD}@postgres:5432/any2pdf
      - ENVIRONMENT=production
    volumes:
      - uploads:/app/uploads

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=any2pdf
      - POSTGRES_USER=any2pdf
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
  uploads:
EOF

# Create Nginx configuration
mkdir nginx
cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream frontend {
        server frontend:80;
    }

    upstream backend {
        server backend:8000;
    }

    server {
        listen 80;

        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /api/ {
            proxy_pass http://backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
EOF
```

## Development Scripts

### 1. Create Utility Scripts
```bash
# Navigate to scripts directory
cd ../scripts

# Create setup script
cat > setup.sh << 'EOF'
#!/bin/bash
set -e

echo "Setting up Any2PDF development environment..."

# Check prerequisites
command -v node >/dev/null 2>&1 || { echo "Node.js is required but not installed. Aborting." >&2; exit 1; }
command -v python >/dev/null 2>&1 || { echo "Python is required but not installed. Aborting." >&2; exit 1; }
command -v ng >/dev/null 2>&1 || { echo "Angular CLI is required but not installed. Run: npm install -g @angular/cli" >&2; exit 1; }

# Setup backend
echo "Setting up backend..."
cd ../backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate  # Windows
pip install -r requirements.txt
alembic upgrade head

# Setup frontend
echo "Setting up frontend..."
cd ../frontend
npm install

echo "Setup complete! Run 'npm start' in frontend and 'uvicorn app.main:app --reload' in backend to start development servers."
EOF

chmod +x setup.sh

# Create deployment script
cat > deploy.sh << 'EOF'
#!/bin/bash
set -e

echo "Deploying Any2PDF..."

# Build frontend
cd ../frontend
npm run build

# Build and deploy with Docker
cd ../
docker-compose -f docker/docker-compose.prod.yml up -d --build

echo "Deployment complete!"
EOF

chmod +x deploy.sh

# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
set -e

BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

mkdir -p $BACKUP_DIR

echo "Creating database backup..."
docker exec any2pdf_postgres pg_dump -U any2pdf any2pdf > "$BACKUP_DIR/db_backup_$TIMESTAMP.sql"

echo "Backup created: $BACKUP_DIR/db_backup_$TIMESTAMP.sql"
EOF

chmod +x backup.sh
```

## GitHub Workflows

### 1. Create CI/CD Workflows
```bash
# Navigate to .github/workflows
cd ../.github/workflows

# Create CI workflow
cat > ci.yml << 'EOF'
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test-frontend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      run: |
        cd frontend
        npm ci

    - name: Run tests
      run: |
        cd frontend
        npm run test -- --watch=false --browsers=ChromeHeadless

    - name: Build
      run: |
        cd frontend
        npm run build

  test-backend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run tests
      run: |
        cd backend
        pytest
EOF

# Create deployment workflow
cat > deploy.yml << 'EOF'
name: Deploy

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        # Add your deployment commands here
        echo "Deploying to production..."
EOF
```

## Environment Configuration

### 1. Create Root Configuration Files
```bash
# Navigate to project root
cd ../..

# Create .gitignore
cat > .gitignore << 'EOF'
# Dependencies
node_modules/
venv/
__pycache__/
*.pyc

# Environment files
.env
.env.local
.env.production

# Build outputs
dist/
build/
*.egg-info/

# Database
*.db
*.sqlite

# Uploads
uploads/
temp/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Coverage
coverage/
.coverage
.nyc_output

# Docker
.docker/
EOF

# Create main docker-compose.yml (symlink to docker version)
ln -sf docker/docker-compose.yml docker-compose.yml
```

## Verification

### 1. Verify Project Structure
```bash
# Check if all directories exist
echo "Verifying project structure..."

# Frontend verification
test -d "frontend/src/app/components" && echo "✓ Frontend components directory exists"
test -d "frontend/src/app/services" && echo "✓ Frontend services directory exists"
test -d "frontend/src/app/models" && echo "✓ Frontend models directory exists"
test -d "frontend/src/app/pages" && echo "✓ Frontend pages directory exists"

# Backend verification
test -d "backend/app/api/endpoints" && echo "✓ Backend API endpoints directory exists"
test -d "backend/app/core" && echo "✓ Backend core directory exists"
test -d "backend/app/services" && echo "✓ Backend services directory exists"
test -d "backend/app/models" && echo "✓ Backend models directory exists"

# Documentation verification
test -f "docs/README.md" && echo "✓ Documentation index exists"
test -f "docs/ARCHITECTURE.md" && echo "✓ Architecture documentation exists"
test -f "docs/SETUP.md" && echo "✓ Setup guide exists"

echo "Project structure verification complete!"
```

### 2. Test Development Servers
```bash
# Test backend
echo "Testing backend setup..."
cd backend
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate  # Windows
python -c "from app.main import app; print('✓ Backend imports successfully')"

# Test frontend
echo "Testing frontend setup..."
cd ../frontend
npm run build --dry-run && echo "✓ Frontend builds successfully"

echo "Setup verification complete!"
```

## Next Steps

After completing this setup guide:

### 1. Start Development Servers
```bash
# Terminal 1 - Backend
cd backend
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate  # Windows
uvicorn app.main:app --reload

# Terminal 2 - Frontend
cd frontend
ng serve
```

### 2. Access Applications
- **Frontend**: http://localhost:4200
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

### 3. Begin Development
- Implement file upload functionality
- Add conversion logic
- Create user interface components
- Write tests

### 4. Deploy with Docker
```bash
docker-compose up -d
```

## Troubleshooting

### Common Issues

#### Frontend Issues
```bash
# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Angular CLI issues
npm uninstall -g @angular/cli
npm install -g @angular/cli@19
```

#### Backend Issues
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt

# Database issues
alembic downgrade base
alembic upgrade head
```

#### Docker Issues
```bash
# Clean Docker
docker system prune -a
docker-compose down -v
docker-compose up --build
```

---

**Last Updated**: January 2025
**Maintained by**: Any2PDF Development Team
