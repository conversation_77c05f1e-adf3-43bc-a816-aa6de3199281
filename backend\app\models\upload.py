from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class FileMetadata(BaseModel):
    id: str
    original_name: str
    stored_name: str
    size: int
    content_type: Optional[str]
    upload_time: datetime

class UploadResponse(BaseModel):
    success: bool
    message: str
    files: List[FileMetadata]
    
class ErrorResponse(BaseModel):
    success: bool = False
    message: str
    error_code: Optional[str] = None