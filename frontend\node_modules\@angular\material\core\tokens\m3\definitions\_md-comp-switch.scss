//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-shape';

@use './md-sys-state';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'disabled-selected-handle-color': map.get($deps, 'md-sys-color', 'surface'),
    'disabled-selected-handle-opacity': if($exclude-hardcoded-values, null, 1),
    'disabled-selected-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-selected-icon-opacity': if($exclude-hardcoded-values, null, 0.38),
    'disabled-selected-track-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-track-opacity': if($exclude-hardcoded-values, null, 0.12),
    'disabled-unselected-handle-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-unselected-handle-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'disabled-unselected-icon-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'disabled-unselected-icon-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'disabled-unselected-track-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'disabled-unselected-track-outline-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'handle-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'pressed-handle-height': if($exclude-hardcoded-values, null, 28px),
    'pressed-handle-width': if($exclude-hardcoded-values, null, 28px),
    'selected-focus-handle-color':
      map.get($deps, 'md-sys-color', 'primary-container'),
    'selected-focus-icon-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'selected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'selected-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'selected-focus-track-color': map.get($deps, 'md-sys-color', 'primary'),
    'selected-handle-color': map.get($deps, 'md-sys-color', 'on-primary'),
    'selected-handle-height': if($exclude-hardcoded-values, null, 24px),
    'selected-handle-width': if($exclude-hardcoded-values, null, 24px),
    'selected-hover-handle-color':
      map.get($deps, 'md-sys-color', 'primary-container'),
    'selected-hover-icon-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'selected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'selected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'selected-hover-track-color': map.get($deps, 'md-sys-color', 'primary'),
    'selected-icon-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'selected-icon-size': if($exclude-hardcoded-values, null, 16px),
    'selected-pressed-handle-color':
      map.get($deps, 'md-sys-color', 'primary-container'),
    'selected-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'selected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'selected-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'selected-pressed-track-color': map.get($deps, 'md-sys-color', 'primary'),
    'selected-track-color': map.get($deps, 'md-sys-color', 'primary'),
    'state-layer-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'state-layer-size': if($exclude-hardcoded-values, null, 40px),
    'track-height': if($exclude-hardcoded-values, null, 32px),
    'track-outline-width': if($exclude-hardcoded-values, null, 2px),
    'track-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'track-width': if($exclude-hardcoded-values, null, 52px),
    'unselected-focus-handle-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-focus-icon-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'unselected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'unselected-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'unselected-focus-track-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'unselected-focus-track-outline-color':
      map.get($deps, 'md-sys-color', 'outline'),
    'unselected-handle-color': map.get($deps, 'md-sys-color', 'outline'),
    'unselected-handle-height': if($exclude-hardcoded-values, null, 16px),
    'unselected-handle-width': if($exclude-hardcoded-values, null, 16px),
    'unselected-hover-handle-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-hover-icon-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'unselected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'unselected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'unselected-hover-track-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'unselected-hover-track-outline-color':
      map.get($deps, 'md-sys-color', 'outline'),
    'unselected-icon-color': map.get($deps, 'md-sys-color', 'surface-variant'),
    'unselected-icon-size': if($exclude-hardcoded-values, null, 16px),
    'unselected-pressed-handle-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'unselected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'unselected-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'unselected-pressed-track-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'unselected-pressed-track-outline-color':
      map.get($deps, 'md-sys-color', 'outline'),
    'unselected-track-color': map.get($deps, 'md-sys-color', 'surface-variant'),
    'unselected-track-outline-color': map.get($deps, 'md-sys-color', 'outline'),
    'with-icon-handle-height': if($exclude-hardcoded-values, null, 24px),
    'with-icon-handle-width': if($exclude-hardcoded-values, null, 24px)
  );
}
