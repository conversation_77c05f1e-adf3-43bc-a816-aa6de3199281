{"version": 3, "file": "directionality-CChdj3az.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/bidi/dir-document-token.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/bidi/directionality.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, InjectionToken, DOCUMENT} from '@angular/core';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nexport const DIR_DOCUMENT = new InjectionToken<Document>('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY,\n});\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function DIR_DOCUMENT_FACTORY(): Document {\n  return inject(DOCUMENT);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EventEmitter, Injectable, OnDestroy, inject, signal} from '@angular/core';\nimport {DIR_DOCUMENT} from './dir-document-token';\n\nexport type Direction = 'ltr' | 'rtl';\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN =\n  /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n\n/** Resolves a string value to a specific direction. */\nexport function _resolveDirectionality(rawValue: string): Direction {\n  const value = rawValue?.toLowerCase() || '';\n\n  if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\n@Injectable({providedIn: 'root'})\nexport class Directionality implements OnDestroy {\n  /** The current 'ltr' or 'rtl' value. */\n  get value() {\n    return this.valueSignal();\n  }\n\n  /**\n   * The current 'ltr' or 'rtl' value.\n   */\n  readonly valueSignal = signal<Direction>('ltr');\n\n  /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n  readonly change = new EventEmitter<Direction>();\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const _document = inject(DIR_DOCUMENT, {optional: true});\n\n    if (_document) {\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.valueSignal.set(_resolveDirectionality(bodyDir || htmlDir || 'ltr'));\n    }\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n"], "names": [], "mappings": ";;;AAUA;;;;;;;;;;;;;;AAcG;MACU,YAAY,GAAG,IAAI,cAAc,CAAW,aAAa,EAAE;AACtE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,oBAAoB;AAC9B,CAAA;AAED;;;;AAIG;SACa,oBAAoB,GAAA;AAClC,IAAA,OAAO,MAAM,CAAC,QAAQ,CAAC;AACzB;;ACxBA;AACA,MAAM,kBAAkB,GACtB,oHAAoH;AAEtH;AACM,SAAU,sBAAsB,CAAC,QAAgB,EAAA;IACrD,MAAM,KAAK,GAAG,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE;AAE3C,IAAA,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE,QAAQ,EAAE;AAC/E,QAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,KAAK,GAAG,KAAK;;IAGpE,OAAO,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;AACxC;AAEA;;;AAGG;MAEU,cAAc,CAAA;;AAEzB,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE;;AAG3B;;AAEG;AACM,IAAA,WAAW,GAAG,MAAM,CAAY,KAAK,CAAC;;AAGtC,IAAA,MAAM,GAAG,IAAI,YAAY,EAAa;AAI/C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAExD,IAAI,SAAS,EAAE;AACb,YAAA,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI;AAC1D,YAAA,MAAM,OAAO,GAAG,SAAS,CAAC,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,GAAG,IAAI;AAChF,YAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,CAAC,OAAO,IAAI,OAAO,IAAI,KAAK,CAAC,CAAC;;;IAI7E,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;;uGA3Bb,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAd,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,cADF,MAAM,EAAA,CAAA;;2FAClB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;;;"}