import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { UploadService } from '../../services/upload.service';
import { UploadProgress, FileMetadata } from '../../models/upload.models';

@Component({
  selector: 'app-file-upload',
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.scss']
})
export class FileUploadComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  isDragOver = false;
  selectedFiles: File[] = [];
  uploadProgress: UploadProgress = { progress: 0, status: 'idle' };
  uploadedFiles: FileMetadata[] = [];
  errorMessage = '';

  constructor(private uploadService: UploadService) {}

  ngOnInit(): void {
    this.uploadService.uploadProgress$
      .pipe(takeUntil(this.destroy$))
      .subscribe(progress => {
        this.uploadProgress = progress;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
    
    if (event.dataTransfer?.files) {
      this.handleFiles(event.dataTransfer.files);
    }
  }

  onFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      this.handleFiles(input.files);
    }
  }

  private handleFiles(fileList: FileList): void {
    this.selectedFiles = [];
    this.errorMessage = '';

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const validationError = this.uploadService.validateFile(file);
      
      if (validationError) {
        this.errorMessage = validationError;
        return;
      }
      
      this.selectedFiles.push(file);
    }
  }

  uploadFiles(): void {
    if (this.selectedFiles.length === 0) return;

    const fileList = this.createFileList(this.selectedFiles);
    
    this.uploadService.uploadFiles(fileList).subscribe({
      next: (response) => {
        if (response && response.success) {
          this.uploadedFiles = response.files;
          this.selectedFiles = [];
        }
      },
      error: (error) => {
        this.errorMessage = error.error?.message || 'Upload failed';
        this.uploadService.resetProgress();
      }
    });
  }

  private createFileList(files: File[]): FileList {
    const dt = new DataTransfer();
    files.forEach(file => dt.items.add(file));
    return dt.files;
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  clearAll(): void {
    this.selectedFiles = [];
    this.uploadedFiles = [];
    this.errorMessage = '';
    this.uploadService.resetProgress();
  }
}
