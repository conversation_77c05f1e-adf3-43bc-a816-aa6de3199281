# System Architecture

## Overview

Any2PDF follows a modern microservice-oriented architecture with a clear separation between frontend and backend services. The system is designed for scalability, maintainability, and easy deployment using containerization.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              Any2PDF System                                │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────┐    HTTP/REST API    ┌─────────────────┐
│   Angular 19+   │◄──────────────────►│   FastAPI       │
│   Frontend      │    (Port 4200)     │   Backend       │
│                 │                    │   (Port 8000)   │
│ • TypeScript    │                    │ • Python 3.12+ │
│ • Angular Mat.  │                    │ • Pydantic     │
│ • Tailwind CSS  │                    │ • Uvicorn      │
│ • NgRx/Services │                    │ • SQLAlchemy   │
└─────────────────┘                    └─────────────────┘
         │                                       │
         │                                       │
         ▼                                       ▼
┌─────────────────┐                    ┌─────────────────┐
│  Static Assets  │                    │ File Processing │
│                 │                    │                 │
│ • Images        │                    │ • ReportLab     │
│ • Styles        │                    │ • Pillow (PIL)  │
│ • Icons         │                    │ • PyPDF2       │
└─────────────────┘                    └─────────────────┘
                                                │
                                                ▼
                                       ┌─────────────────┐
                                       │   Database      │
                                       │                 │
                                       │ • SQLite (dev)  │
                                       │ • PostgreSQL    │
                                       │   (production)  │
                                       │ • Alembic       │
                                       └─────────────────┘
                                                │
                                                ▼
                                       ┌─────────────────┐
                                       │  File Storage   │
                                       │                 │
                                       │ • Local FS      │
                                       │ • Temp Files    │
                                       │ • Auto Cleanup  │
                                       └─────────────────┘
```

## Project Structure

```
Any2PDF/
├── frontend/                    # Angular Application
│   ├── src/
│   │   ├── app/                # Angular app module
│   │   │   ├── components/     # Reusable components
│   │   │   │   ├── file-upload/
│   │   │   │   ├── conversion-panel/
│   │   │   │   ├── progress-bar/
│   │   │   │   └── download-manager/
│   │   │   ├── services/       # API and business logic services
│   │   │   │   ├── api.service.ts
│   │   │   │   ├── file.service.ts
│   │   │   │   └── conversion.service.ts
│   │   │   ├── models/         # TypeScript interfaces/models
│   │   │   │   ├── file.model.ts
│   │   │   │   ├── conversion.model.ts
│   │   │   │   └── api-response.model.ts
│   │   │   ├── pages/          # Page components
│   │   │   │   ├── home/
│   │   │   │   ├── convert/
│   │   │   │   └── about/
│   │   │   ├── guards/         # Route guards
│   │   │   ├── interceptors/   # HTTP interceptors
│   │   │   └── pipes/          # Custom pipes
│   │   ├── assets/             # Static assets
│   │   │   ├── images/
│   │   │   ├── icons/
│   │   │   └── styles/
│   │   ├── environments/       # Environment configurations
│   │   │   ├── environment.ts
│   │   │   └── environment.prod.ts
│   │   ├── styles.css          # Global styles
│   │   └── main.ts             # Application bootstrap
│   ├── angular.json            # Angular CLI configuration
│   ├── package.json            # Frontend dependencies
│   ├── tsconfig.json           # TypeScript config
│   ├── tailwind.config.js      # Tailwind CSS config
│   └── Dockerfile              # Frontend container
├── backend/                     # FastAPI Application
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py             # FastAPI app entry point
│   │   ├── api/                # API routes
│   │   │   ├── __init__.py
│   │   │   ├── deps.py         # Dependencies
│   │   │   └── endpoints/      # API endpoint modules
│   │   │       ├── __init__.py
│   │   │       ├── conversion.py
│   │   │       ├── files.py
│   │   │       └── health.py
│   │   ├── core/               # Core functionality
│   │   │   ├── __init__.py
│   │   │   ├── config.py       # Configuration
│   │   │   ├── security.py     # Security utilities
│   │   │   └── database.py     # Database connection
│   │   ├── services/           # Business logic
│   │   │   ├── __init__.py
│   │   │   ├── pdf_service.py  # PDF operations
│   │   │   ├── image_service.py # Image operations
│   │   │   └── file_service.py # File management
│   │   ├── models/             # Pydantic models
│   │   │   ├── __init__.py
│   │   │   ├── conversion.py
│   │   │   ├── file.py
│   │   │   └── user.py
│   │   ├── schemas/            # Database schemas
│   │   │   ├── __init__.py
│   │   │   └── conversion.py
│   │   └── utils/              # Utility functions
│   │       ├── __init__.py
│   │       ├── file_utils.py
│   │       └── validation.py
│   ├── tests/                  # Backend tests
│   │   ├── __init__.py
│   │   ├── conftest.py
│   │   ├── test_api/
│   │   ├── test_services/
│   │   └── test_utils/
│   ├── alembic/                # Database migrations
│   │   ├── versions/
│   │   ├── env.py
│   │   └── alembic.ini
│   ├── requirements.txt        # Python dependencies
│   ├── .env.example           # Environment variables template
│   └── Dockerfile             # Backend container
├── docker/                     # Docker configuration
│   ├── docker-compose.yml      # Development environment
│   ├── docker-compose.prod.yml # Production environment
│   ├── nginx/                  # Reverse proxy config
│   └── postgres/               # Database initialization
├── docs/                       # Documentation
│   ├── README.md               # Documentation index
│   ├── ARCHITECTURE.md         # This file
│   ├── FEATURES.md             # Feature specifications
│   ├── ROADMAP.md              # Development roadmap
│   ├── TODO.md                 # Development tasks
│   └── API.md                  # API documentation (planned)
├── scripts/                    # Utility scripts
│   ├── setup.sh               # Development setup
│   ├── deploy.sh              # Deployment script
│   └── backup.sh              # Database backup
├── .github/                    # GitHub workflows
│   └── workflows/
│       ├── ci.yml
│       └── deploy.yml
├── .gitignore                  # Git ignore rules
├── docker-compose.yml          # Main Docker Compose
├── README.md                   # Project overview
└── LICENSE                     # Project license
```

## Component Architecture

### Frontend (Angular)

#### Core Components
- **AppComponent**: Root component with navigation and layout
- **FileUploadComponent**: Drag & drop file upload interface
- **ConversionPanelComponent**: Conversion options and controls
- **ProgressBarComponent**: Real-time conversion progress
- **DownloadManagerComponent**: File download and management

#### Services
- **ApiService**: HTTP client wrapper for backend communication
- **FileService**: File validation, upload, and management
- **ConversionService**: Conversion logic and state management
- **NotificationService**: User notifications and alerts

#### State Management
- **Angular Services**: For simple state management
- **RxJS Observables**: For reactive data flow
- **Local Storage**: For user preferences and session data

### Backend (FastAPI)

#### API Layer
- **Endpoints**: RESTful API endpoints for file operations
- **Dependencies**: Shared dependencies and middleware
- **Validation**: Request/response validation with Pydantic

#### Business Logic Layer
- **Services**: Core business logic for conversions
- **Utils**: Utility functions and helpers
- **Models**: Data models and schemas

#### Data Layer
- **Database**: SQLAlchemy ORM with PostgreSQL
- **File Storage**: Local filesystem with cleanup policies
- **Cache**: Redis for temporary data and session management

## Data Flow

### File Upload Process
```
1. User selects files in Angular frontend
2. Frontend validates file types and sizes
3. Files uploaded to FastAPI backend via multipart/form-data
4. Backend validates files and stores temporarily
5. Backend returns upload confirmation with file IDs
6. Frontend displays upload success and conversion options
```

### Conversion Process
```
1. User initiates conversion with selected options
2. Frontend sends conversion request to backend
3. Backend queues conversion job (async processing)
4. Backend processes file using appropriate service
5. Backend stores converted file and updates job status
6. Frontend polls for completion or receives WebSocket update
7. User downloads converted file
8. Backend cleans up temporary files after timeout
```

## Security Architecture

### Authentication & Authorization
- **JWT Tokens**: For user authentication (future phase)
- **API Keys**: For programmatic access (future phase)
- **Rate Limiting**: Request throttling per IP/user
- **CORS**: Cross-origin resource sharing configuration

### File Security
- **File Validation**: MIME type and content validation
- **Size Limits**: Configurable file size restrictions
- **Malware Scanning**: File content security checks
- **Temporary Storage**: Automatic cleanup of uploaded files

### Network Security
- **HTTPS**: SSL/TLS encryption for all communications
- **Security Headers**: HSTS, CSP, X-Frame-Options
- **Input Sanitization**: SQL injection and XSS prevention
- **Error Handling**: Secure error messages without information leakage

## Database Design

### Core Tables
```sql
-- Conversion jobs tracking
conversions (
    id: UUID PRIMARY KEY,
    input_filename: VARCHAR,
    output_filename: VARCHAR,
    input_format: VARCHAR,
    output_format: VARCHAR,
    status: ENUM('pending', 'processing', 'completed', 'failed'),
    created_at: TIMESTAMP,
    completed_at: TIMESTAMP,
    file_size: BIGINT,
    user_id: UUID (future)
);

-- File metadata
files (
    id: UUID PRIMARY KEY,
    filename: VARCHAR,
    original_name: VARCHAR,
    mime_type: VARCHAR,
    size: BIGINT,
    path: VARCHAR,
    created_at: TIMESTAMP,
    expires_at: TIMESTAMP
);

-- User sessions (future)
sessions (
    id: UUID PRIMARY KEY,
    session_token: VARCHAR,
    user_agent: VARCHAR,
    ip_address: INET,
    created_at: TIMESTAMP,
    expires_at: TIMESTAMP
);
```

## Deployment Architecture

### Development Environment
- **Docker Compose**: Local development with hot reload
- **Volume Mounts**: Source code mounted for live editing
- **Environment Variables**: Development-specific configuration

### Production Environment
- **Docker Swarm**: Container orchestration
- **Load Balancer**: Nginx reverse proxy
- **SSL Termination**: Let's Encrypt certificates
- **Health Checks**: Container health monitoring

### Scaling Strategy
- **Horizontal Scaling**: Multiple backend instances
- **Database Scaling**: Read replicas for query optimization
- **File Storage**: Distributed storage for large files
- **CDN**: Content delivery network for static assets

## Performance Considerations

### Frontend Optimization
- **Lazy Loading**: Route-based code splitting
- **Tree Shaking**: Unused code elimination
- **Compression**: Gzip/Brotli compression
- **Caching**: Browser caching strategies

### Backend Optimization
- **Async Processing**: Non-blocking I/O operations
- **Connection Pooling**: Database connection optimization
- **Caching**: Redis for frequently accessed data
- **File Streaming**: Efficient large file handling

### Database Optimization
- **Indexing**: Optimized query performance
- **Partitioning**: Large table management
- **Connection Limits**: Resource management
- **Query Optimization**: Efficient SQL queries

## Monitoring & Observability

### Application Monitoring
- **Health Checks**: Endpoint availability monitoring
- **Performance Metrics**: Response time and throughput
- **Error Tracking**: Exception monitoring and alerting
- **Resource Usage**: CPU, memory, and disk monitoring

### Logging Strategy
- **Structured Logging**: JSON-formatted logs
- **Log Levels**: Debug, info, warning, error, critical
- **Log Aggregation**: Centralized log collection
- **Log Retention**: Configurable retention policies

### Metrics Collection
- **Business Metrics**: Conversion rates and user activity
- **Technical Metrics**: System performance and errors
- **Custom Metrics**: Application-specific measurements
- **Dashboards**: Real-time monitoring visualizations

---

**Last Updated**: January 2025  
**Next Review**: February 2025
