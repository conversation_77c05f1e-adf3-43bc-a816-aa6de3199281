<div class="upload-container">
  <h2>Upload Files</h2>
  
  <!-- Drag and Drop Area -->
  <div 
    class="drop-zone"
    [class.drag-over]="isDragOver"
    (dragover)="onDragOver($event)"
    (dragleave)="onDragLeave($event)"
    (drop)="onDrop($event)"
  >
    <div class="drop-zone-content">
      <mat-icon class="upload-icon">cloud_upload</mat-icon>
      <p>Drag and drop files here or</p>
      <input 
        type="file" 
        #fileInput 
        multiple 
        (change)="onFileSelect($event)"
        style="display: none"
      >
      <button mat-raised-button color="primary" (click)="fileInput.click()">
        Choose Files
      </button>
    </div>
  </div>

  <!-- Error Message -->
  <mat-error *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </mat-error>

  <!-- Selected Files -->
  <div *ngIf="selectedFiles.length > 0" class="selected-files">
    <h3>Selected Files</h3>
    <mat-list>
      <mat-list-item *ngFor="let file of selectedFiles; let i = index">
        <mat-icon matListItemIcon>description</mat-icon>
        <div matListItemTitle>{{ file.name }}</div>
        <div matListItemLine>{{ (file.size / 1024 / 1024).toFixed(2) }} MB</div>
        <button mat-icon-button (click)="removeFile(i)">
          <mat-icon>close</mat-icon>
        </button>
      </mat-list-item>
    </mat-list>
    
    <div class="upload-actions">
      <button 
        mat-raised-button 
        color="primary" 
        (click)="uploadFiles()"
        [disabled]="uploadProgress.status === 'uploading'"
      >
        Upload Files
      </button>
      <button mat-button (click)="clearAll()">Clear All</button>
    </div>
  </div>

  <!-- Upload Progress -->
  <div *ngIf="uploadProgress.status === 'uploading'" class="upload-progress">
    <mat-progress-bar [value]="uploadProgress.progress"></mat-progress-bar>
    <p>Uploading... {{ uploadProgress.progress }}%</p>
  </div>

  <!-- Uploaded Files -->
  <div *ngIf="uploadedFiles.length > 0" class="uploaded-files">
    <h3>Uploaded Files</h3>
    <mat-list>
      <mat-list-item *ngFor="let file of uploadedFiles">
        <mat-icon matListItemIcon color="primary">check_circle</mat-icon>
        <div matListItemTitle>{{ file.original_name }}</div>
        <div matListItemLine>{{ (file.size / 1024 / 1024).toFixed(2) }} MB</div>
      </mat-list-item>
    </mat-list>
  </div>
</div>
