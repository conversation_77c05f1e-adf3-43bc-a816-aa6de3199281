@use 'sass:map';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, tree);

/// Generates custom tokens for the mat-tree.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-tree
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: (
    container-background-color: map.get($systems, md-sys-color, surface),
    node-text-color: map.get($systems, md-sys-color, on-surface),
    node-text-font: map.get($systems, md-sys-typescale, body-large-font),
    node-text-size: map.get($systems, md-sys-typescale, body-large-size),
    node-text-weight: map.get($systems, md-sys-typescale, body-large-weight),
  );

  @return token-definition.namespace-tokens($prefix, $tokens, $token-slots);
}
