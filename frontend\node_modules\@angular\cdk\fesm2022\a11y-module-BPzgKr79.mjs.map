{"version": 3, "file": "a11y-module-BPzgKr79.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/interactivity-checker/interactivity-checker.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/focus-trap/focus-trap.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/live-announcer/live-announcer-tokens.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/live-announcer/live-announcer.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/high-contrast-mode/high-contrast-mode-detector.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/a11y-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform} from '../../platform';\nimport {Injectable, inject} from '@angular/core';\n\n/**\n * Configuration for the isFocusable method.\n */\nexport class IsFocusableConfig {\n  /**\n   * Whether to count an element as focusable even if it is not currently visible.\n   */\n  ignoreVisibility: boolean = false;\n}\n\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\n@Injectable({providedIn: 'root'})\nexport class InteractivityChecker {\n  private _platform = inject(Platform);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element: HTMLElement): boolean {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element: HTMLElement): boolean {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element: HTMLElement): boolean {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n\n    const frameElement = getFrameElement(getWindow(element));\n\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n\n    return element.tabIndex >= 0;\n  }\n\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element: HTMLElement, config?: IsFocusableConfig): boolean {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return (\n      isPotentiallyFocusable(element) &&\n      !this.isDisabled(element) &&\n      (config?.ignoreVisibility || this.isVisible(element))\n    );\n  }\n}\n\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window: Window) {\n  try {\n    return window.frameElement as HTMLElement;\n  } catch {\n    return null;\n  }\n}\n\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element: HTMLElement): boolean {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(\n    element.offsetWidth ||\n    element.offsetHeight ||\n    (typeof element.getClientRects === 'function' && element.getClientRects().length)\n  );\n}\n\n/** Gets whether an element's  */\nfunction isNativeFormElement(element: Node) {\n  let nodeName = element.nodeName.toLowerCase();\n  return (\n    nodeName === 'input' ||\n    nodeName === 'select' ||\n    nodeName === 'button' ||\n    nodeName === 'textarea'\n  );\n}\n\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element: HTMLElement): boolean {\n  return isInputElement(element) && element.type == 'hidden';\n}\n\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element: HTMLElement): boolean {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n\n/** Gets whether an element is an input element. */\nfunction isInputElement(element: HTMLElement): element is HTMLInputElement {\n  return element.nodeName.toLowerCase() == 'input';\n}\n\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element: HTMLElement): element is HTMLAnchorElement {\n  return element.nodeName.toLowerCase() == 'a';\n}\n\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element: HTMLElement): boolean {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element: HTMLElement): number | null {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element: HTMLElement): boolean {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && (element as HTMLInputElement).type;\n\n  return (\n    inputType === 'text' ||\n    inputType === 'password' ||\n    nodeName === 'select' ||\n    nodeName === 'textarea'\n  );\n}\n\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element: HTMLElement): boolean {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n\n  return (\n    isNativeFormElement(element) ||\n    isAnchorWithHref(element) ||\n    element.hasAttribute('contenteditable') ||\n    hasValidTabIndex(element)\n  );\n}\n\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node: HTMLElement): Window {\n  // ownerDocument is null if `node` itself *is* a document.\n  return (node.ownerDocument && node.ownerDocument.defaultView) || window;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform, _getFocusedElementPierceShadowDom} from '../../platform';\n\nimport {\n  AfterContentInit,\n  Directive,\n  DoCheck,\n  ElementRef,\n  Injectable,\n  Injector,\n  Input,\n  NgZone,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  afterNextRender,\n  booleanAttribute,\n  inject,\n  DOCUMENT,\n} from '@angular/core';\nimport {InteractivityChecker} from '../interactivity-checker/interactivity-checker';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '../../private';\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nexport class FocusTrap {\n  private _startAnchor: HTMLElement | null;\n  private _endAnchor: HTMLElement | null;\n  private _hasAttached = false;\n\n  // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n  protected startAnchorListener = () => this.focusLastTabbableElement();\n  protected endAnchorListener = () => this.focusFirstTabbableElement();\n\n  /** Whether the focus trap is active. */\n  get enabled(): boolean {\n    return this._enabled;\n  }\n  set enabled(value: boolean) {\n    this._enabled = value;\n\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  protected _enabled: boolean = true;\n\n  constructor(\n    readonly _element: HTMLElement,\n    private _checker: InteractivityChecker,\n    readonly _ngZone: NgZone,\n    readonly _document: Document,\n    deferAnchors = false,\n    /** @breaking-change 20.0.0 param to become required */\n    readonly _injector?: Injector,\n  ) {\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors(): boolean {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor!.addEventListener('focus', this.startAnchorListener);\n      }\n\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor!.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor!, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor!, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n\n    return this._hasAttached;\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options?: FocusOptions): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options?: FocusOptions): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options?: FocusOptions): Promise<boolean> {\n    return new Promise<boolean>(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  private _getRegionBoundary(bound: 'start' | 'end'): HTMLElement | null {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(\n      `[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`,\n    ) as NodeListOf<HTMLElement>;\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(\n            `Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n              `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n              `attribute will be removed in 8.0.0.`,\n            markers[i],\n          );\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(\n            `Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n              `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n              `will be removed in 8.0.0.`,\n            markers[i],\n          );\n        }\n      }\n    }\n\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length\n      ? markers[markers.length - 1]\n      : this._getLastTabbableElement(this._element);\n  }\n\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options?: FocusOptions): boolean {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(\n      `[cdk-focus-initial], ` + `[cdkFocusInitial]`,\n    ) as HTMLElement;\n\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if (\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n        redirectToElement.hasAttribute(`cdk-focus-initial`)\n      ) {\n        console.warn(\n          `Found use of deprecated attribute 'cdk-focus-initial', ` +\n            `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n            `will be removed in 8.0.0`,\n          redirectToElement,\n        );\n      }\n\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if (\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n        !this._checker.isFocusable(redirectToElement)\n      ) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement) as HTMLElement;\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n\n      redirectToElement.focus(options);\n      return true;\n    }\n\n    return this.focusFirstTabbableElement(options);\n  }\n\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options?: FocusOptions): boolean {\n    const redirectToElement = this._getRegionBoundary('start');\n\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n\n    return !!redirectToElement;\n  }\n\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options?: FocusOptions): boolean {\n    const redirectToElement = this._getRegionBoundary('end');\n\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n\n    return !!redirectToElement;\n  }\n\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached(): boolean {\n    return this._hasAttached;\n  }\n\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  private _getFirstTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    const children = root.children;\n\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild =\n        children[i].nodeType === this._document.ELEMENT_NODE\n          ? this._getFirstTabbableElement(children[i] as HTMLElement)\n          : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  private _getLastTabbableElement(root: HTMLElement): HTMLElement | null {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    // Iterate in reverse DOM order.\n    const children = root.children;\n\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild =\n        children[i].nodeType === this._document.ELEMENT_NODE\n          ? this._getLastTabbableElement(children[i] as HTMLElement)\n          : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n\n  /** Creates an anchor element. */\n  private _createAnchor(): HTMLElement {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  private _toggleAnchorTabIndex(isEnabled: boolean, anchor: HTMLElement) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  protected toggleAnchors(enabled: boolean) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n\n  /** Executes a function when the zone is stable. */\n  private _executeOnStable(fn: () => any): void {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {injector: this._injector});\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n\n/**\n * Factory that allows easy instantiation of focus traps.\n */\n@Injectable({providedIn: 'root'})\nexport class FocusTrapFactory {\n  private _checker = inject(InteractivityChecker);\n  private _ngZone = inject(NgZone);\n\n  private _document = inject(DOCUMENT);\n  private _injector = inject(Injector);\n\n  constructor(...args: unknown[]);\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element: HTMLElement, deferCaptureElements: boolean = false): FocusTrap {\n    return new FocusTrap(\n      element,\n      this._checker,\n      this._ngZone,\n      this._document,\n      deferCaptureElements,\n      this._injector,\n    );\n  }\n}\n\n/** Directive for trapping focus within a region. */\n@Directive({\n  selector: '[cdkTrapFocus]',\n  exportAs: 'cdkTrapFocus',\n})\nexport class CdkTrapFocus implements OnDestroy, AfterContentInit, OnChanges, DoCheck {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _focusTrapFactory = inject(FocusTrapFactory);\n\n  /** Underlying FocusTrap instance. */\n  focusTrap: FocusTrap;\n\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  private _previouslyFocusedElement: HTMLElement | null = null;\n\n  /** Whether the focus trap is active. */\n  @Input({alias: 'cdkTrapFocus', transform: booleanAttribute})\n  get enabled(): boolean {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value: boolean) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  @Input({alias: 'cdkTrapFocusAutoCapture', transform: booleanAttribute}) autoCapture: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const platform = inject(Platform);\n\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const autoCaptureChange = changes['autoCapture'];\n\n    if (\n      autoCaptureChange &&\n      !autoCaptureChange.firstChange &&\n      this.autoCapture &&\n      this.focusTrap?.hasAttached()\n    ) {\n      this._captureFocus();\n    }\n  }\n\n  private _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n// The tokens for the live announcer are defined in a separate file from LiveAnnouncer\n// as a workaround for https://github.com/angular/angular/issues/22559\n\n/** Possible politeness levels. */\nexport type AriaLivePoliteness = 'off' | 'polite' | 'assertive';\n\nexport const LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken<HTMLElement | null>(\n  'liveAnnouncerElement',\n  {\n    providedIn: 'root',\n    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY(): null {\n  return null;\n}\n\n/** Object that can be used to configure the default options for the LiveAnnouncer. */\nexport interface LiveAnnouncerDefaultOptions {\n  /** Default politeness for the announcements. */\n  politeness?: AriaLivePoliteness;\n\n  /** Default duration for the announcement messages. */\n  duration?: number;\n}\n\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nexport const LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken<LiveAnnouncerDefaultOptions>(\n  'LIVE_ANNOUNCER_DEFAULT_OPTIONS',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ContentObserver} from '../../observers';\n\nimport {\n  Directive,\n  ElementRef,\n  Injectable,\n  Input,\n  NgZone,\n  OnDestroy,\n  inject,\n  DOCUMENT,\n} from '@angular/core';\nimport {Subscription} from 'rxjs';\nimport {\n  AriaLivePoliteness,\n  LiveAnnouncerDefaultOptions,\n  LIVE_ANNOUNCER_ELEMENT_TOKEN,\n  LIVE_ANNOUNCER_DEFAULT_OPTIONS,\n} from './live-announcer-tokens';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '../../private';\n\nlet uniqueIds = 0;\n\n@Injectable({providedIn: 'root'})\nexport class LiveAnnouncer implements OnDestroy {\n  private _ngZone = inject(NgZone);\n  private _defaultOptions = inject<LiveAnnouncerDefaultOptions>(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n    optional: true,\n  });\n\n  private _liveElement: HTMLElement;\n  private _document = inject(DOCUMENT);\n  private _previousTimeout: ReturnType<typeof setTimeout>;\n  private _currentPromise: Promise<void> | undefined;\n  private _currentResolve: (() => void) | undefined;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, {optional: true});\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n\n  /**\n   * Announces a message to screen readers.\n   * @param message Message to be announced to the screen reader.\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string): Promise<void>;\n\n  /**\n   * Announces a message to screen readers.\n   * @param message Message to be announced to the screen reader.\n   * @param politeness The politeness of the announcer element.\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string, politeness?: AriaLivePoliteness): Promise<void>;\n\n  /**\n   * Announces a message to screen readers.\n   * @param message Message to be announced to the screen reader.\n   * @param duration Time in milliseconds after which to clear out the announcer element. Note\n   *   that this takes effect after the message has been added to the DOM, which can be up to\n   *   100ms after `announce` has been called.\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string, duration?: number): Promise<void>;\n\n  /**\n   * Announces a message to screen readers.\n   * @param message Message to be announced to the screen reader.\n   * @param politeness The politeness of the announcer element.\n   * @param duration Time in milliseconds after which to clear out the announcer element. Note\n   *   that this takes effect after the message has been added to the DOM, which can be up to\n   *   100ms after `announce` has been called.\n   * @returns Promise that will be resolved when the message is added to the DOM.\n   */\n  announce(message: string, politeness?: AriaLivePoliteness, duration?: number): Promise<void>;\n\n  announce(message: string, ...args: any[]): Promise<void> {\n    const defaultOptions = this._defaultOptions;\n    let politeness: AriaLivePoliteness | undefined;\n    let duration: number | undefined;\n\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n\n    this.clear();\n    clearTimeout(this._previousTimeout);\n\n    if (!politeness) {\n      politeness =\n        defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => (this._currentResolve = resolve));\n      }\n\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n\n      return this._currentPromise;\n    });\n  }\n\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null!;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n\n  private _createLiveElement(): HTMLElement {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n\n    this._document.body.appendChild(liveEl);\n\n    return liveEl;\n  }\n\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  private _exposeAnnouncerToModals(id: string) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll(\n      'body > .cdk-overlay-container [aria-modal=\"true\"]',\n    );\n\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n}\n\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\n@Directive({\n  selector: '[cdkAriaLive]',\n  exportAs: 'cdkAriaLive',\n})\nexport class CdkAriaLive implements OnDestroy {\n  private _elementRef = inject(ElementRef);\n  private _liveAnnouncer = inject(LiveAnnouncer);\n  private _contentObserver = inject(ContentObserver);\n  private _ngZone = inject(NgZone);\n\n  /** The aria-live politeness level to use when announcing messages. */\n  @Input('cdkAriaLive')\n  get politeness(): AriaLivePoliteness {\n    return this._politeness;\n  }\n  set politeness(value: AriaLivePoliteness) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  private _politeness: AriaLivePoliteness = 'polite';\n\n  /** Time in milliseconds after which to clear out the announcer element. */\n  @Input('cdkAriaLiveDuration') duration: number;\n\n  private _previousAnnouncedText?: string;\n  private _subscription: Subscription | null;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, Injectable, OnDestroy, DOCUMENT} from '@angular/core';\nimport {BreakpointObserver} from '../../layout';\nimport {Platform} from '../../platform';\n\nimport {Subscription} from 'rxjs';\n\n/** Set of possible high-contrast mode backgrounds. */\nexport enum HighContrastMode {\n  NONE,\n  BLACK_ON_WHITE,\n  WHITE_ON_BLACK,\n}\n\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nexport const BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nexport const WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n\n/** CSS class applied to the document body when in high-contrast mode. */\nexport const HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\n@Injectable({providedIn: 'root'})\nexport class HighContrastModeDetector implements OnDestroy {\n  private _platform = inject(Platform);\n\n  /**\n   * Figuring out the high contrast mode and adding the body classes can cause\n   * some expensive layouts. This flag is used to ensure that we only do it once.\n   */\n  private _hasCheckedHighContrastMode: boolean;\n  private _document = inject(DOCUMENT);\n  private _breakpointSubscription: Subscription;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    this._breakpointSubscription = inject(BreakpointObserver)\n      .observe('(forced-colors: active)')\n      .subscribe(() => {\n        if (this._hasCheckedHighContrastMode) {\n          this._hasCheckedHighContrastMode = false;\n          this._applyBodyHighContrastModeCssClasses();\n        }\n      });\n  }\n\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode(): HighContrastMode {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle =\n      documentWindow && documentWindow.getComputedStyle\n        ? documentWindow.getComputedStyle(testElement)\n        : null;\n    const computedColor = ((computedStyle && computedStyle.backgroundColor) || '').replace(\n      / /g,\n      '',\n    );\n    testElement.remove();\n\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n\n  ngOnDestroy(): void {\n    this._breakpointSubscription.unsubscribe();\n  }\n\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses(): void {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(\n        HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS,\n        BLACK_ON_WHITE_CSS_CLASS,\n        WHITE_ON_BLACK_CSS_CLASS,\n      );\n      this._hasCheckedHighContrastMode = true;\n\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ObserversModule} from '../observers';\nimport {NgModule, inject} from '@angular/core';\nimport {CdkMonitorFocus} from './focus-monitor/focus-monitor';\nimport {CdkTrapFocus} from './focus-trap/focus-trap';\nimport {HighContrastModeDetector} from './high-contrast-mode/high-contrast-mode-detector';\nimport {CdkAriaLive} from './live-announcer/live-announcer';\n\n@NgModule({\n  imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n  exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n})\nexport class A11yModule {\n  constructor() {\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAWA;;AAEG;MACU,iBAAiB,CAAA;AAC5B;;AAEG;IACH,gBAAgB,GAAY,KAAK;AAClC;AAED;AACA;AACA;AAEA;;;AAGG;MAEU,oBAAoB,CAAA;AACvB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAGpC,IAAA,WAAA,GAAA;AAEA;;;;;AAKG;AACH,IAAA,UAAU,CAAC,OAAoB,EAAA;;;AAG7B,QAAA,OAAO,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC;;AAGzC;;;;;;;AAOG;AACH,IAAA,SAAS,CAAC,OAAoB,EAAA;AAC5B,QAAA,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,UAAU,KAAK,SAAS;;AAGnF;;;;;;AAMG;AACH,IAAA,UAAU,CAAC,OAAoB,EAAA;;AAE7B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC7B,YAAA,OAAO,KAAK;;QAGd,MAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExD,IAAI,YAAY,EAAE;;YAEhB,IAAI,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;AACzC,gBAAA,OAAO,KAAK;;;YAId,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;AACjC,gBAAA,OAAO,KAAK;;;QAIhB,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE;AAC7C,QAAA,IAAI,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAE7C,QAAA,IAAI,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;AAC3C,YAAA,OAAO,aAAa,KAAK,CAAC,CAAC;;QAG7B,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,QAAQ,EAAE;;;;AAIlD,YAAA,OAAO,KAAK;;;AAId,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE;AACrF,YAAA,OAAO,KAAK;;AAGd,QAAA,IAAI,QAAQ,KAAK,OAAO,EAAE;;;YAGxB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;AACrC,gBAAA,OAAO,KAAK;;;;AAId,YAAA,OAAO,aAAa,KAAK,CAAC,CAAC;;AAG7B,QAAA,IAAI,QAAQ,KAAK,OAAO,EAAE;;;;;AAKxB,YAAA,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;AACxB,gBAAA,OAAO,KAAK;;;;AAId,YAAA,IAAI,aAAa,KAAK,IAAI,EAAE;AAC1B,gBAAA,OAAO,IAAI;;;;;AAKb,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC;;AAGnE,QAAA,OAAO,OAAO,CAAC,QAAQ,IAAI,CAAC;;AAG9B;;;;;;AAMG;IACH,WAAW,CAAC,OAAoB,EAAE,MAA0B,EAAA;;;AAG1D,QAAA,QACE,sBAAsB,CAAC,OAAO,CAAC;AAC/B,YAAA,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACzB,aAAC,MAAM,EAAE,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;;uGA1H9C,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAApB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,cADR,MAAM,EAAA,CAAA;;2FAClB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAgIhC;;;;AAIG;AACH,SAAS,eAAe,CAAC,MAAc,EAAA;AACrC,IAAA,IAAI;QACF,OAAO,MAAM,CAAC,YAA2B;;AACzC,IAAA,MAAM;AACN,QAAA,OAAO,IAAI;;AAEf;AAEA;AACA,SAAS,WAAW,CAAC,OAAoB,EAAA;;;AAGvC,IAAA,OAAO,CAAC,EACN,OAAO,CAAC,WAAW;AACnB,QAAA,OAAO,CAAC,YAAY;AACpB,SAAC,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAClF;AACH;AAEA;AACA,SAAS,mBAAmB,CAAC,OAAa,EAAA;IACxC,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE;IAC7C,QACE,QAAQ,KAAK,OAAO;AACpB,QAAA,QAAQ,KAAK,QAAQ;AACrB,QAAA,QAAQ,KAAK,QAAQ;QACrB,QAAQ,KAAK,UAAU;AAE3B;AAEA;AACA,SAAS,aAAa,CAAC,OAAoB,EAAA;IACzC,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ;AAC5D;AAEA;AACA,SAAS,gBAAgB,CAAC,OAAoB,EAAA;IAC5C,OAAO,eAAe,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;AACjE;AAEA;AACA,SAAS,cAAc,CAAC,OAAoB,EAAA;IAC1C,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,OAAO;AAClD;AAEA;AACA,SAAS,eAAe,CAAC,OAAoB,EAAA;IAC3C,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,GAAG;AAC9C;AAEA;AACA,SAAS,gBAAgB,CAAC,OAAoB,EAAA;AAC5C,IAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;AACvE,QAAA,OAAO,KAAK;;IAGd,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC;AAC/C,IAAA,OAAO,CAAC,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;AACvD;AAEA;;;AAGG;AACH,SAAS,gBAAgB,CAAC,OAAoB,EAAA;AAC5C,IAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE;AAC9B,QAAA,OAAO,IAAI;;;AAIb,IAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AAErE,IAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AACxC;AAEA;AACA,SAAS,wBAAwB,CAAC,OAAoB,EAAA;IACpD,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE;IAC7C,IAAI,SAAS,GAAG,QAAQ,KAAK,OAAO,IAAK,OAA4B,CAAC,IAAI;IAE1E,QACE,SAAS,KAAK,MAAM;AACpB,QAAA,SAAS,KAAK,UAAU;AACxB,QAAA,QAAQ,KAAK,QAAQ;QACrB,QAAQ,KAAK,UAAU;AAE3B;AAEA;;;AAGG;AACH,SAAS,sBAAsB,CAAC,OAAoB,EAAA;;AAElD,IAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;AAC1B,QAAA,OAAO,KAAK;;AAGd,IAAA,QACE,mBAAmB,CAAC,OAAO,CAAC;QAC5B,gBAAgB,CAAC,OAAO,CAAC;AACzB,QAAA,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC;AACvC,QAAA,gBAAgB,CAAC,OAAO,CAAC;AAE7B;AAEA;AACA,SAAS,SAAS,CAAC,IAAiB,EAAA;;AAElC,IAAA,OAAO,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,MAAM;AACzE;;AClPA;;;;;;AAMG;MACU,SAAS,CAAA;AAwBT,IAAA,QAAA;AACD,IAAA,QAAA;AACC,IAAA,OAAA;AACA,IAAA,SAAA;AAGA,IAAA,SAAA;AA7BH,IAAA,YAAY;AACZ,IAAA,UAAU;IACV,YAAY,GAAG,KAAK;;IAGlB,mBAAmB,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE;IAC3D,iBAAiB,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE;;AAGpE,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;QAErB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;YACxC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC;YACpD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC;;;IAG5C,QAAQ,GAAY,IAAI;IAElC,WACW,CAAA,QAAqB,EACtB,QAA8B,EAC7B,OAAe,EACf,SAAmB,EAC5B,YAAY,GAAG,KAAK;;IAEX,SAAoB,EAAA;QANpB,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACT,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACP,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAS,CAAA,SAAA,GAAT,SAAS;QAGT,IAAS,CAAA,SAAA,GAAT,SAAS;QAElB,IAAI,CAAC,YAAY,EAAE;YACjB,IAAI,CAAC,aAAa,EAAE;;;;IAKxB,OAAO,GAAA;AACL,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY;AACrC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU;QAEjC,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC;YAClE,WAAW,CAAC,MAAM,EAAE;;QAGtB,IAAI,SAAS,EAAE;YACb,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC;YAC9D,SAAS,CAAC,MAAM,EAAE;;QAGpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI;AAC1C,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK;;AAG3B;;;;;AAKG;IACH,aAAa,GAAA;;AAEX,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,OAAO,IAAI;;AAGb,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE;gBACxC,IAAI,CAAC,YAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC;;AAGxE,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;gBACtC,IAAI,CAAC,UAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC;;AAEtE,SAAC,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;AAC5B,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,YAAa,EAAE,IAAI,CAAC,QAAQ,CAAC;AACxE,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;AAClF,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;QAG1B,OAAO,IAAI,CAAC,YAAY;;AAG1B;;;;AAIG;AACH,IAAA,4BAA4B,CAAC,OAAsB,EAAA;AACjD,QAAA,OAAO,IAAI,OAAO,CAAU,OAAO,IAAG;AACpC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;AACzE,SAAC,CAAC;;AAGJ;;;;;AAKG;AACH,IAAA,kCAAkC,CAAC,OAAsB,EAAA;AACvD,QAAA,OAAO,IAAI,OAAO,CAAU,OAAO,IAAG;AACpC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;AAC/E,SAAC,CAAC;;AAGJ;;;;;AAKG;AACH,IAAA,iCAAiC,CAAC,OAAsB,EAAA;AACtD,QAAA,OAAO,IAAI,OAAO,CAAU,OAAO,IAAG;AACpC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC;AAC9E,SAAC,CAAC;;AAGJ;;;;AAIG;AACK,IAAA,kBAAkB,CAAC,KAAsB,EAAA;;QAE/C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAC5C,CAAqB,kBAAA,EAAA,KAAK,KAAK,GAAG,CAAA,eAAA,EAAkB,KAAK,CAAK,GAAA,CAAA,GAAG,cAAc,KAAK,CAAA,CAAA,CAAG,CAC7D;AAE5B,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;AAEvC,gBAAA,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAA,UAAA,EAAa,KAAK,CAAA,CAAE,CAAC,EAAE;AACjD,oBAAA,OAAO,CAAC,IAAI,CACV,CAAA,6CAAA,EAAgD,KAAK,CAAK,GAAA,CAAA;AACxD,wBAAA,CAAA,mBAAA,EAAsB,KAAK,CAA4B,0BAAA,CAAA;AACvD,wBAAA,CAAA,mCAAA,CAAqC,EACvC,OAAO,CAAC,CAAC,CAAC,CACX;;AACI,qBAAA,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAA,iBAAA,EAAoB,KAAK,CAAA,CAAE,CAAC,EAAE;AAC/D,oBAAA,OAAO,CAAC,IAAI,CACV,CAAA,oDAAA,EAAuD,KAAK,CAAK,GAAA,CAAA;AAC/D,wBAAA,CAAA,mBAAA,EAAsB,KAAK,CAAsC,oCAAA,CAAA;AACjE,wBAAA,CAAA,yBAAA,CAA2B,EAC7B,OAAO,CAAC,CAAC,CAAC,CACX;;;;AAKP,QAAA,IAAI,KAAK,IAAI,OAAO,EAAE;YACpB,OAAO,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC;;QAEnF,OAAO,OAAO,CAAC;cACX,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;cAC1B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAGjD;;;AAGG;AACH,IAAA,mBAAmB,CAAC,OAAsB,EAAA;;AAExC,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CACnD,CAAuB,qBAAA,CAAA,GAAG,CAAmB,iBAAA,CAAA,CAC/B;QAEhB,IAAI,iBAAiB,EAAE;;AAErB,YAAA,IACE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC9C,gBAAA,iBAAiB,CAAC,YAAY,CAAC,CAAmB,iBAAA,CAAA,CAAC,EACnD;gBACA,OAAO,CAAC,IAAI,CACV,CAAyD,uDAAA,CAAA;oBACvD,CAA0D,wDAAA,CAAA;oBAC1D,CAA0B,wBAAA,CAAA,EAC5B,iBAAiB,CAClB;;;;AAKH,YAAA,IACE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC9C,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAC7C;AACA,gBAAA,OAAO,CAAC,IAAI,CAAC,wDAAwD,EAAE,iBAAiB,CAAC;;YAG3F,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAE;gBACjD,MAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAgB;AACtF,gBAAA,cAAc,EAAE,KAAK,CAAC,OAAO,CAAC;gBAC9B,OAAO,CAAC,CAAC,cAAc;;AAGzB,YAAA,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC;AAChC,YAAA,OAAO,IAAI;;AAGb,QAAA,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;;AAGhD;;;AAGG;AACH,IAAA,yBAAyB,CAAC,OAAsB,EAAA;QAC9C,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;QAE1D,IAAI,iBAAiB,EAAE;AACrB,YAAA,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC;;QAGlC,OAAO,CAAC,CAAC,iBAAiB;;AAG5B;;;AAGG;AACH,IAAA,wBAAwB,CAAC,OAAsB,EAAA;QAC7C,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;QAExD,IAAI,iBAAiB,EAAE;AACrB,YAAA,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC;;QAGlC,OAAO,CAAC,CAAC,iBAAiB;;AAG5B;;AAEG;IACH,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,YAAY;;;AAIlB,IAAA,wBAAwB,CAAC,IAAiB,EAAA;AAChD,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACrE,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAE9B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,MAAM,aAAa,GACjB,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC;kBACpC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAgB;kBACxD,IAAI;YAEV,IAAI,aAAa,EAAE;AACjB,gBAAA,OAAO,aAAa;;;AAIxB,QAAA,OAAO,IAAI;;;AAIL,IAAA,uBAAuB,CAAC,IAAiB,EAAA;AAC/C,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACrE,YAAA,OAAO,IAAI;;;AAIb,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAE9B,QAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7C,YAAA,MAAM,aAAa,GACjB,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC;kBACpC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAgB;kBACvD,IAAI;YAEV,IAAI,aAAa,EAAE;AACjB,gBAAA,OAAO,aAAa;;;AAIxB,QAAA,OAAO,IAAI;;;IAIL,aAAa,GAAA;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;QAClD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;AACjD,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC;AAC3C,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,CAAC;AAC7C,QAAA,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;AAC1C,QAAA,OAAO,MAAM;;AAGf;;;;AAIG;IACK,qBAAqB,CAAC,SAAkB,EAAE,MAAmB,EAAA;;;QAGnE,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC;;AAGvF;;;AAGG;AACO,IAAA,aAAa,CAAC,OAAgB,EAAA;QACtC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;YACxC,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC;;;;AAKhD,IAAA,gBAAgB,CAAC,EAAa,EAAA;;AAEpC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,eAAe,CAAC,EAAE,EAAE,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAAC;;aAC1C;YACL,UAAU,CAAC,EAAE,CAAC;;;AAGnB;AAED;;AAEG;MAEU,gBAAgB,CAAA;AACnB,IAAA,QAAQ,GAAG,MAAM,CAAC,oBAAoB,CAAC;AACvC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAExB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAGpC,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;;AAG5D;;;;;;AAMG;AACH,IAAA,MAAM,CAAC,OAAoB,EAAE,oBAAA,GAAgC,KAAK,EAAA;QAChE,OAAO,IAAI,SAAS,CAClB,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,EACd,oBAAoB,EACpB,IAAI,CAAC,SAAS,CACf;;uGA3BQ,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAgChC;MAKa,YAAY,CAAA;AACf,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;;AAGpD,IAAA,SAAS;;IAGD,yBAAyB,GAAuB,IAAI;;AAG5D,IAAA,IACI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,KAAK;;IAEzC,IAAI,OAAO,CAAC,KAAc,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK;;;AAIlC;;;AAGG;AACqE,IAAA,WAAW;AAInF,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AAEjC,QAAA,IAAI,QAAQ,CAAC,SAAS,EAAE;AACtB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC;;;IAIxF,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;;;AAIzB,QAAA,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAClC,YAAA,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE;AACtC,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI;;;IAIzC,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE;AAE/B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,aAAa,EAAE;;;IAIxB,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE;AACnD,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;;;AAIlC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,MAAM,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC;AAEhD,QAAA,IACE,iBAAiB;YACjB,CAAC,iBAAiB,CAAC,WAAW;AAC9B,YAAA,IAAI,CAAC,WAAW;AAChB,YAAA,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,EAC7B;YACA,IAAI,CAAC,aAAa,EAAE;;;IAIhB,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,yBAAyB,GAAG,iCAAiC,EAAE;AACpE,QAAA,IAAI,CAAC,SAAS,EAAE,4BAA4B,EAAE;;uGA7ErC,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,cAAA,EAAA,SAAA,EAWmB,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,yBAAA,EAAA,aAAA,EAcL,gBAAgB,CAAA,EAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAzB1D,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,cAAc;AACzB,iBAAA;wDAaK,OAAO,EAAA,CAAA;sBADV,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,cAAc,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAca,WAAW,EAAA,CAAA;sBAAlF,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,yBAAyB,EAAE,SAAS,EAAE,gBAAgB,EAAC;;;MCna3D,4BAA4B,GAAG,IAAI,cAAc,CAC5D,sBAAsB,EACtB;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,oCAAoC;AAC9C,CAAA;AAGH;;;;AAIG;SACa,oCAAoC,GAAA;AAClD,IAAA,OAAO,IAAI;AACb;AAWA;MACa,8BAA8B,GAAG,IAAI,cAAc,CAC9D,gCAAgC;;ACflC,IAAI,SAAS,GAAG,CAAC;MAGJ,aAAa,CAAA;AAChB,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,eAAe,GAAG,MAAM,CAA8B,8BAA8B,EAAE;AAC5F,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;AAEM,IAAA,YAAY;AACZ,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,gBAAgB;AAChB,IAAA,eAAe;AACf,IAAA,eAAe;AAIvB,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,4BAA4B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAC3E,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,kBAAkB,EAAE;;AAuC/D,IAAA,QAAQ,CAAC,OAAe,EAAE,GAAG,IAAW,EAAA;AACtC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe;AAC3C,QAAA,IAAI,UAA0C;AAC9C,QAAA,IAAI,QAA4B;AAEhC,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AACpD,YAAA,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;;aACb;AACL,YAAA,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,IAAI;;QAG/B,IAAI,CAAC,KAAK,EAAE;AACZ,QAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;QAEnC,IAAI,CAAC,UAAU,EAAE;YACf,UAAU;AACR,gBAAA,cAAc,IAAI,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,GAAG,QAAQ;;AAGtF,QAAA,IAAI,QAAQ,IAAI,IAAI,IAAI,cAAc,EAAE;AACtC,YAAA,QAAQ,GAAG,cAAc,CAAC,QAAQ;;;QAIpC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,EAAE,UAAU,CAAC;AAEvD,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;YACxB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;;;;;;;AAQrD,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACzC,YAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACzB,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,CAAC;;AAGjF,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACnC,YAAA,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,MAAK;AACtC,gBAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,OAAO;AAEvC,gBAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AAChC,oBAAA,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,EAAE,QAAQ,CAAC;;;;AAKlE,gBAAA,IAAI,CAAC,eAAe,IAAI;gBACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,SAAS;aACxD,EAAE,GAAG,CAAC;YAEP,OAAO,IAAI,CAAC,eAAe;AAC7B,SAAC,CAAC;;AAGJ;;;;AAIG;IACH,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,EAAE;;;IAItC,WAAW,GAAA;AACT,QAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACnC,QAAA,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAK;AACzB,QAAA,IAAI,CAAC,eAAe,IAAI;QACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,SAAS;;IAGjD,kBAAkB,GAAA;QACxB,MAAM,YAAY,GAAG,4BAA4B;QACjD,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,YAAY,CAAC;QAC5E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;;AAGlD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChD,YAAA,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;;AAG9B,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC;AAClC,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC;AAE3C,QAAA,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;AAC1C,QAAA,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;AAC1C,QAAA,MAAM,CAAC,EAAE,GAAG,sBAAsB,SAAS,EAAE,EAAE;QAE/C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AAEvC,QAAA,OAAO,MAAM;;AAGf;;;;AAIG;AACK,IAAA,wBAAwB,CAAC,EAAU,EAAA;;;;;;;QAOzC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAC5C,mDAAmD,CACpD;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;YACvB,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC;YAEhD,IAAI,CAAC,QAAQ,EAAE;AACb,gBAAA,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;;iBAC9B,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtC,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC;;;;uGAjL/C,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAb,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,cADD,MAAM,EAAA,CAAA;;2FAClB,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAwLhC;;;AAGG;MAKU,WAAW,CAAA;AACd,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AAChC,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;AACtC,IAAA,gBAAgB,GAAG,MAAM,CAAC,eAAe,CAAC;AAC1C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;;AAGhC,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAAyB,EAAA;AACtC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,WAAW,GAAG,KAAK,GAAG,QAAQ;AAC9E,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;AAC9B,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,gBAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;AAChC,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;;AAEtB,aAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACvD,gBAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,MAAK;;oBAEpE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW;;;AAI9D,oBAAA,IAAI,WAAW,KAAK,IAAI,CAAC,sBAAsB,EAAE;AAC/C,wBAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC;AAC1E,wBAAA,IAAI,CAAC,sBAAsB,GAAG,WAAW;;AAE7C,iBAAC,CAAC;AACJ,aAAC,CAAC;;;IAGE,WAAW,GAAuB,QAAQ;;AAGpB,IAAA,QAAQ;AAE9B,IAAA,sBAAsB;AACtB,IAAA,aAAa;AAIrB,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;;IAG5D,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;;;uGAlDzB,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,CAAA,aAAA,EAAA,YAAA,CAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,EAAA,UAAA,CAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBAJvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,aAAa;AACxB,iBAAA;wDASK,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,aAAa;gBA8BU,QAAQ,EAAA,CAAA;sBAArC,KAAK;uBAAC,qBAAqB;;;ACtP9B;IACY;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,gBAAA,CAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc;AACd,IAAA,gBAAA,CAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc;AAChB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,GAI3B,EAAA,CAAA,CAAA;AAED;AACO,MAAM,wBAAwB,GAAG,kCAAkC;AAE1E;AACO,MAAM,wBAAwB,GAAG,kCAAkC;AAE1E;AACO,MAAM,mCAAmC,GAAG,0BAA0B;AAE7E;;;;;;;;;;AAUG;MAEU,wBAAwB,CAAA;AAC3B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAEpC;;;AAGG;AACK,IAAA,2BAA2B;AAC3B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,uBAAuB;AAI/B,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,kBAAkB;aACrD,OAAO,CAAC,yBAAyB;aACjC,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE;AACpC,gBAAA,IAAI,CAAC,2BAA2B,GAAG,KAAK;gBACxC,IAAI,CAAC,oCAAoC,EAAE;;AAE/C,SAAC,CAAC;;;IAIN,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAO,gBAAgB,CAAC,IAAI;;;;;QAM9B,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;AACvD,QAAA,WAAW,CAAC,KAAK,CAAC,eAAe,GAAG,YAAY;AAChD,QAAA,WAAW,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU;QACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;;;;;QAM5C,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,MAAM;AAC3D,QAAA,MAAM,aAAa,GACjB,cAAc,IAAI,cAAc,CAAC;AAC/B,cAAE,cAAc,CAAC,gBAAgB,CAAC,WAAW;cAC3C,IAAI;QACV,MAAM,aAAa,GAAG,CAAC,CAAC,aAAa,IAAI,aAAa,CAAC,eAAe,KAAK,EAAE,EAAE,OAAO,CACpF,IAAI,EACJ,EAAE,CACH;QACD,WAAW,CAAC,MAAM,EAAE;QAEpB,QAAQ,aAAa;;AAEnB,YAAA,KAAK,YAAY;;AAEjB,YAAA,KAAK,eAAe;AACpB,YAAA,KAAK,eAAe;gBAClB,OAAO,gBAAgB,CAAC,cAAc;;AAExC,YAAA,KAAK,kBAAkB;;AAEvB,YAAA,KAAK,kBAAkB;gBACrB,OAAO,gBAAgB,CAAC,cAAc;;QAE1C,OAAO,gBAAgB,CAAC,IAAI;;IAG9B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE;;;IAI5C,oCAAoC,GAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACxF,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS;YACjD,WAAW,CAAC,MAAM,CAChB,mCAAmC,EACnC,wBAAwB,EACxB,wBAAwB,CACzB;AACD,YAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI;AAEvC,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,EAAE;AACvC,YAAA,IAAI,IAAI,KAAK,gBAAgB,CAAC,cAAc,EAAE;AAC5C,gBAAA,WAAW,CAAC,GAAG,CAAC,mCAAmC,EAAE,wBAAwB,CAAC;;AACzE,iBAAA,IAAI,IAAI,KAAK,gBAAgB,CAAC,cAAc,EAAE;AACnD,gBAAA,WAAW,CAAC,GAAG,CAAC,mCAAmC,EAAE,wBAAwB,CAAC;;;;uGAxFzE,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAxB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,wBAAwB,cADZ,MAAM,EAAA,CAAA;;2FAClB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBADpC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;MCtBnB,UAAU,CAAA;AACrB,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,CAAC,wBAAwB,CAAC,CAAC,oCAAoC,EAAE;;uGAF9D,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,EAHX,OAAA,EAAA,CAAA,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,CAC3D,EAAA,OAAA,EAAA,CAAA,WAAW,EAAE,YAAY,EAAE,eAAe,CAAA,EAAA,CAAA;AAEzC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,YAHX,eAAe,CAAA,EAAA,CAAA;;2FAGd,UAAU,EAAA,UAAA,EAAA,CAAA;kBAJtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,CAAC;AACtE,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,eAAe,CAAC;AACtD,iBAAA;;;;;"}