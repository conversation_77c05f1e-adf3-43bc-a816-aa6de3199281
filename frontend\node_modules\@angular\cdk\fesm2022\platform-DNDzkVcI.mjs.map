{"version": 3, "file": "platform-DNDzkVcI.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/platform/platform.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, Injectable, PLATFORM_ID} from '@angular/core';\nimport {isPlatformBrowser} from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator: boolean;\n\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && (Intl as any).v8BreakIterator;\n} catch {\n  hasV8BreakIterator = false;\n}\n\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\n@Injectable({providedIn: 'root'})\nexport class Platform {\n  private _platformId = inject(PLATFORM_ID);\n\n  // We want to use the Angular platform check because if the Document is shimmed\n  // without the navigator, the following checks will fail. This is preferred because\n  // sometimes the Document may be shimmed without the user's knowledge or intention\n  /** Whether the Angular application is being rendered in the browser. */\n  isBrowser: boolean = this._platformId\n    ? isPlatformBrowser(this._platformId)\n    : typeof document === 'object' && !!document;\n\n  /** Whether the current browser is Microsoft Edge. */\n  EDGE: boolean = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n\n  /** Whether the current rendering engine is Microsoft Trident. */\n  TRIDENT: boolean = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n\n  // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n  /** Whether the current rendering engine is Blink. */\n  BLINK: boolean =\n    this.isBrowser &&\n    !!((window as any).chrome || hasV8BreakIterator) &&\n    typeof CSS !== 'undefined' &&\n    !this.EDGE &&\n    !this.TRIDENT;\n\n  // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n  // ensure that Webkit runs standalone and is not used as another engine's base.\n  /** Whether the current rendering engine is WebKit. */\n  WEBKIT: boolean =\n    this.isBrowser &&\n    /AppleWebKit/i.test(navigator.userAgent) &&\n    !this.BLINK &&\n    !this.EDGE &&\n    !this.TRIDENT;\n\n  /** Whether the current platform is Apple iOS. */\n  IOS: boolean =\n    this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n\n  // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n  // them self as Gecko-like browsers and modify the userAgent's according to that.\n  // Since we only cover one explicit Firefox case, we can simply check for Firefox\n  // instead of having an unstable check for Gecko.\n  /** Whether the current browser is Firefox. */\n  FIREFOX: boolean = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n\n  /** Whether the current platform is Android. */\n  // Trident on mobile adds the android platform to the userAgent to trick detections.\n  ANDROID: boolean = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n\n  // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n  // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n  // Safari browser should also use Webkit as its layout engine.\n  /** Whether the current browser is Safari. */\n  SAFARI: boolean = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n\n  /** Backwards-compatible constructor. */\n  constructor(..._args: unknown[]);\n\n  constructor() {}\n}\n"], "names": [], "mappings": ";;;;AAWA;AACA;AACA,IAAI,kBAA2B;AAE/B;AACA;AACA;AACA;AACA;AACA,IAAI;IACF,kBAAkB,GAAG,OAAO,IAAI,KAAK,WAAW,IAAK,IAAY,CAAC,eAAe;AACnF;AAAE,MAAM;IACN,kBAAkB,GAAG,KAAK;AAC5B;AAEA;;;AAGG;MAEU,QAAQ,CAAA;AACX,IAAA,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;;;;;IAMzC,SAAS,GAAY,IAAI,CAAC;AACxB,UAAE,iBAAiB,CAAC,IAAI,CAAC,WAAW;UAClC,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ;;AAG9C,IAAA,IAAI,GAAY,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;;AAGrE,IAAA,OAAO,GAAY,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;;;IAIhF,KAAK,GACH,IAAI,CAAC,SAAS;AACd,QAAA,CAAC,EAAG,MAAc,CAAC,MAAM,IAAI,kBAAkB,CAAC;QAChD,OAAO,GAAG,KAAK,WAAW;QAC1B,CAAC,IAAI,CAAC,IAAI;QACV,CAAC,IAAI,CAAC,OAAO;;;;IAKf,MAAM,GACJ,IAAI,CAAC,SAAS;AACd,QAAA,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;QACxC,CAAC,IAAI,CAAC,KAAK;QACX,CAAC,IAAI,CAAC,IAAI;QACV,CAAC,IAAI,CAAC,OAAO;;IAGf,GAAG,GACD,IAAI,CAAC,SAAS,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,IAAI,MAAM,CAAC;;;;;;AAO3F,IAAA,OAAO,GAAY,IAAI,CAAC,SAAS,IAAI,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;;;AAIrF,IAAA,OAAO,GAAY,IAAI,CAAC,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;;;;;AAM1F,IAAA,MAAM,GAAY,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,MAAM;AAKtF,IAAA,WAAA,GAAA;uGA5DW,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAR,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,cADI,MAAM,EAAA,CAAA;;2FAClB,QAAQ,EAAA,UAAA,EAAA,CAAA;kBADpB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;;;"}