
from pydantic_settings import BaseSettings
from typing import List
from functools import lru_cache

class Settings(BaseSettings):
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    environment: str = "development"
    
    # File Upload Configuration
    max_file_size: int = 52428800  # 50MB in bytes
    allowed_extensions: List[str] = [".jpg", ".jpeg", ".png", ".pdf", ".docx", ".txt"]
    upload_dir: str = "./uploads"
    
    # CORS Configuration
    cors_origins: List[str] = ["http://localhost:4200"]
    
    class Config:
        env_file = ".env"

@lru_cache()
def get_settings():
    return Settings()

