export interface FileMetadata {
  id: string;
  original_name: string;
  stored_name: string;
  size: number;
  content_type?: string;
  upload_time: string;
}

export interface UploadResponse {
  success: boolean;
  message: string;
  files: FileMetadata[];
}

export interface UploadProgress {
  progress: number;
  status: 'idle' | 'uploading' | 'completed' | 'error';
}

export interface ErrorResponse {
  success: false;
  message: string;
  error_code?: string;
}