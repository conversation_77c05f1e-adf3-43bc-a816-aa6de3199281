from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from typing import List
import os
import uuid
import shutil
from pathlib import Path
from datetime import datetime

from app.models.upload import UploadResponse, FileMetadata
from app.services.file_service import FileService
from app.core.config import get_settings

settings = get_settings()

app = FastAPI(
    title="Any2PDF API",
    description="File conversion API for Any2PDF application",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:4200"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create upload directory
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.post("/upload", response_model=UploadResponse)
async def upload_files(
    files: List[UploadFile] = File(...),
    file_service: FileService = Depends()
):
    try:
        uploaded_files = []
        
        for file in files:
            # Validate file
            await file_service.validate_file(file)
            
            # Generate unique filename
            file_id = str(uuid.uuid4())
            file_extension = Path(file.filename).suffix
            unique_filename = f"{file_id}{file_extension}"
            file_path = UPLOAD_DIR / unique_filename
            
            # Save file
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Create file metadata
            file_metadata = FileMetadata(
                id=file_id,
                original_name=file.filename,
                stored_name=unique_filename,
                size=file.size,
                content_type=file.content_type,
                upload_time=datetime.utcnow()
            )
            
            uploaded_files.append(file_metadata)
        
        return UploadResponse(
            success=True,
            message=f"Successfully uploaded {len(uploaded_files)} file(s)",
            files=uploaded_files
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))