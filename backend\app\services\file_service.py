
from fastapi import UploadFile, HTTPException
from pathlib import Path
from typing import List
from app.core.config import get_settings

class FileService:
    def __init__(self):
        self.settings = get_settings()
    
    async def validate_file(self, file: UploadFile) -> None:
        """Validate uploaded file"""
        
        # Check file size
        if file.size and file.size > self.settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"File size {file.size} exceeds maximum allowed size of {self.settings.max_file_size} bytes"
            )
        
        # Check file extension
        if file.filename:
            file_extension = Path(file.filename).suffix.lower()
            if file_extension not in self.settings.allowed_extensions:
                raise HTTPException(
                    status_code=415,
                    detail=f"File type {file_extension} not supported. Allowed types: {', '.join(self.settings.allowed_extensions)}"
                )
        
        # Check if file is empty
        if file.size == 0:
            raise HTTPException(
                status_code=400,
                detail="Empty files are not allowed"
            )

