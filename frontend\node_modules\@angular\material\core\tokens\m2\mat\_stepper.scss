@use 'sass:map';
@use '../../token-definition';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, stepper);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    // Border radius for step header on focus
    header-focus-state-layer-shape: 0,
    // Border radius for step header on hover
    header-hover-state-layer-shape: 0,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return map.merge(private-get-color-palette-color-tokens($theme, primary), (
    // Background for stepper container.
    container-color: inspection.get-theme-color($theme, background, card),
    // Color of the line indicator connecting the steps.
    line-color: inspection.get-theme-color($theme, foreground, divider),
    // Background color of the header while hovered.
    header-hover-state-layer-color: inspection.get-theme-color($theme, background, hover),
    // Background color of the header while focused.
    header-focus-state-layer-color: inspection.get-theme-color($theme, background, hover),
    // Color of the text inside the step header.
    header-label-text-color: inspection.get-theme-color($theme, foreground, secondary-text),
    // Color for the "optional" label in the step header.
    header-optional-label-text-color: inspection.get-theme-color(
        $theme, foreground, secondary-text),
    // Color of the header text when a step is selected.
    header-selected-state-label-text-color: inspection.get-theme-color($theme, foreground, text),
    // Color of the header text when a step is in an error state.
    header-error-state-label-text-color: inspection.get-theme-color($theme, warn, text),
    // Background color of the header icon.
    header-icon-background-color: inspection.get-theme-color($theme, foreground, secondary-text),
    // Foreground color of the header icon in the error state.
    header-error-state-icon-foreground-color: inspection.get-theme-color($theme, warn, text),
    // Background color of the header icon in the error state.
    header-error-state-icon-background-color: transparent,
  ));
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    // Font family of the entire stepper.
    container-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    // Font family of the text inside the step header.
    header-label-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    // Size of the text inside the step header.
    header-label-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    // Weight of the text inside the step header.
    header-label-text-weight: inspection.get-theme-typography($theme, body-2, font-weight),
    // Color of the header text when a step is in an error state.
    header-error-state-label-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    // Size of the header text in the selected state.
    header-selected-state-label-text-size: inspection.get-theme-typography(
        $theme, body-1, font-size),
    // Weight of the header text in the selected state.
    header-selected-state-label-text-weight: inspection.get-theme-typography(
        $theme, body-1, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -4);
  $height-config: (
    0: 72px,
    -1: 68px,
    -2: 64px,
    -3: 60px,
    -4: 42px,
  );

  @return (
    header-height: map.get($height-config, $scale),
  );
}

// Generates the tokens used to theme the stepper based on a palette.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  $active-state-foreground: inspection.get-theme-color($theme, $palette-name, default-contrast);
  $active-state-background: inspection.get-theme-color($theme, $palette-name);

  @return (
    // Foreground color of the header icon.
    header-icon-foreground-color: $active-state-foreground,
    // Background color of the header icon in the selected state.
    header-selected-state-icon-background-color: $active-state-background,
    // Foreground color of the header icon in the selected state.
    header-selected-state-icon-foreground-color: $active-state-foreground,
    // Background color of the header icon in the selected state.
    header-done-state-icon-background-color: $active-state-background,
    // Foreground color of the header icon in the selected state.
    header-done-state-icon-foreground-color: $active-state-foreground,
    // Background color of the header icon in the editing state.
    header-edit-state-icon-background-color: $active-state-background,
    // Foreground color of the header icon in the editing state.
    header-edit-state-icon-foreground-color: $active-state-foreground,
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
