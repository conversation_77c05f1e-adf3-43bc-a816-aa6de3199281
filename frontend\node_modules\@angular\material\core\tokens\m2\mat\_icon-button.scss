@use 'sass:meta';
@use '../../token-definition';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, icon-button);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;

  @return (
    // Color of the element that shows the hover, focus and pressed states.
    state-layer-color: inspection.get-theme-color($theme, foreground, base),

    // Color of the element that shows the hover, focus and pressed states while disabled.
    disabled-state-layer-color: inspection.get-theme-color($theme, foreground, base),

    // Color of the ripple element.
    ripple-color: inspection.get-theme-color($theme, foreground, base, 0.1),

    // Opacity of the ripple when the button is hovered.
    hover-state-layer-opacity: if($is-dark, 0.08, 0.04),

    // Opacity of the ripple when the button is focused.
    focus-state-layer-opacity: if($is-dark, 0.24, 0.12),

    // Opacity of the ripple when the button is pressed.
    pressed-state-layer-opacity: if($is-dark, 0.24, 0.12),
  );
}

// Generates the mapping for the properties that change based on the button palette color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  $color: inspection.get-theme-color($theme, $palette-name);
  $ripple-opacity: 0.1;

  @return (
    state-layer-color: $color,
    ripple-color: if(
      meta.type-of($color) == color,
      rgba($color, $ripple-opacity),
      inspection.get-theme-color($theme, foreground, base, $ripple-opacity)),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    touch-target-display: if($density-scale < -1, none, block),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
