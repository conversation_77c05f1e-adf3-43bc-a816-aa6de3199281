@use 'sass:map';
@use 'sass:meta';
@use '../../token-definition';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, standard-button-toggle);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    shape: 4px,
    hover-state-layer-opacity: 0.04,
    focus-state-layer-opacity: 0.12,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $theme-divider-color: inspection.get-theme-color($theme, foreground, divider);

  // By default the theme usually has an rgba color for the dividers, which can
  // stack up with the background of a button toggle. This can cause the border
  // of a selected toggle to look different from an unselected one. We use a solid
  // color to ensure that the border always stays the same.
  $divider-color: if(
      meta.type-of($theme-divider-color) == color,
      theming.private-rgba-to-hex(
          $theme-divider-color, inspection.get-theme-color($theme, background, card)),
      $theme-divider-color);

  @return (
    text-color: inspection.get-theme-color($theme, foreground, text),
    background-color: inspection.get-theme-color($theme, background, card),
    state-layer-color: sass-utils.safe-color-change(
        inspection.get-theme-color($theme, background, focused-button), $alpha: 1),
    selected-state-background-color: inspection.get-theme-color(
        $theme, background, selected-button),
    selected-state-text-color: inspection.get-theme-color($theme, foreground, text),
    disabled-state-text-color: inspection.get-theme-color($theme, foreground, disabled-button),
    disabled-state-background-color: inspection.get-theme-color($theme, background, card),
    disabled-selected-state-text-color: inspection.get-theme-color($theme, foreground, text),
    disabled-selected-state-background-color:
      inspection.get-theme-color($theme, background, selected-disabled-button),
    divider-color: $divider-color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    label-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    label-text-line-height: inspection.get-theme-typography($theme, body-1, line-height),
    label-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    label-text-tracking: inspection.get-theme-typography($theme, body-1, letter-spacing),
    label-text-weight: inspection.get-theme-typography($theme, body-1, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -4);
  $size-scale: (
    0: 48px,
    -1: 44px,
    -2: 40px,
    -3: 36px,
    -4: 24px,
  );

  @return (
    height: map.get($size-scale, $density-scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
