//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'action-focus-label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'action-focus-state-layer-color': map.get($deps, 'md-sys-color', 'primary'),
    'action-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'action-hover-label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'action-hover-state-layer-color': map.get($deps, 'md-sys-color', 'primary'),
    'action-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'action-label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'action-label-text-font':
      map.get($deps, 'md-sys-typescale', 'label-large-font'),
    'action-label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'label-large-line-height'),
    'action-label-text-size':
      map.get($deps, 'md-sys-typescale', 'label-large-size'),
    'action-label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'label-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.rich-tooltip.action.label-text.tracking cannot be represented in the
    // "font" property shorthand. Consider using the discrete properties instead.
    'action-label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'label-large-weight')
          map.get($deps, 'md-sys-typescale', 'label-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-large-font')
      ),
    'action-label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-large-weight'),
    'action-pressed-label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'action-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'action-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level2'),
    'container-shadow-color': map.get($deps, 'md-sys-color', 'shadow'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-small'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'subhead-color': map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'subhead-font': map.get($deps, 'md-sys-typescale', 'title-small-font'),
    'subhead-line-height':
      map.get($deps, 'md-sys-typescale', 'title-small-line-height'),
    'subhead-size': map.get($deps, 'md-sys-typescale', 'title-small-size'),
    'subhead-tracking':
      map.get($deps, 'md-sys-typescale', 'title-small-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.rich-tooltip.subhead.tracking cannot be represented in the "font"
    // property shorthand. Consider using the discrete properties instead.
    'subhead-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'title-small-weight')
          map.get($deps, 'md-sys-typescale', 'title-small-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'title-small-line-height'
          ) map.get($deps, 'md-sys-typescale', 'title-small-font')
      ),
    'subhead-weight': map.get($deps, 'md-sys-typescale', 'title-small-weight'),
    'supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'supporting-text-font':
      map.get($deps, 'md-sys-typescale', 'body-medium-font'),
    'supporting-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-medium-line-height'),
    'supporting-text-size':
      map.get($deps, 'md-sys-typescale', 'body-medium-size'),
    'supporting-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-medium-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.rich-tooltip.supporting-text.tracking cannot be represented in the
    // "font" property shorthand. Consider using the discrete properties instead.
    'supporting-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'body-medium-weight')
          map.get($deps, 'md-sys-typescale', 'body-medium-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-medium-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-medium-font')
      ),
    'supporting-text-weight':
      map.get($deps, 'md-sys-typescale', 'body-medium-weight')
  );
}
