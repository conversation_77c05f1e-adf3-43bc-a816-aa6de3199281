@use 'sass:map';
@use '../../../theming/inspection';
@use '../../../style/elevation';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, fab);

@function get-unthemable-tokens() {
  @return (
    container-shape: 50%,
    container-elevation-shadow: elevation.get-box-shadow(6),
    focus-container-elevation-shadow: elevation.get-box-shadow(8),
    hover-container-elevation-shadow: elevation.get-box-shadow(8),
    pressed-container-elevation-shadow: elevation.get-box-shadow(12),

    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    container-height: null,
    container-surface-tint-layer-color: null,
    container-width: null,
    icon-size: null,
    icon-color: null,

    focus-icon-color: null,
    focus-outline-color: null,
    focus-outline-width: null,
    focus-state-layer-color: null,
    focus-state-layer-opacity: null,

    hover-icon-color: null,
    hover-state-layer-color: null,
    hover-state-layer-opacity: null,

    lowered-container-elevation: null,
    lowered-focus-container-elevation: null,
    lowered-hover-container-elevation: null,
    lowered-pressed-container-elevation: null,

    pressed-icon-color: null,
    pressed-ripple-color: null,
    pressed-ripple-opacity: null,
    pressed-state-layer-color: null,
    pressed-state-layer-opacity: null,

    container-elevation: null,
    focus-container-elevation: null,
    hover-container-elevation: null,
    pressed-container-elevation: null,
    container-shadow-color: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    // Background color of the FAB.
    container-color: inspection.get-theme-color($theme, background, card),
  );
}

// Generates the mapping for the properties that change based on the FAB palette color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  @return (
    container-color: inspection.get-theme-color($theme, $palette-name, default),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return map.merge(
    get-unthemable-tokens(),
    map.merge(
      get-color-tokens(token-definition.$placeholder-color-config),
      map.merge(
        get-typography-tokens(token-definition.$placeholder-typography-config),
        get-density-tokens(token-definition.$placeholder-density-config)
      )
    )
  );
}
