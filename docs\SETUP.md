# Development Setup Guide

This guide will walk you through setting up the Any2PDF project structure from scratch using PowerShell commands on Windows.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Project Initialization](#project-initialization)
3. [Frontend Setup (Angular)](#frontend-setup-angular)
4. [Backend Setup (FastAPI)](#backend-setup-fastapi)
5. [Documentation and Configuration](#documentation-and-configuration)
6. [Verification](#verification)

## Prerequisites

Before starting, ensure you have the following installed:

```powershell
# Check versions
node --version    # Should be 20.16.0+ ✅
npm --version     # Should be 10.9.0+ ✅
python --version  # Should be 3.12+  ✅
git --version     # Any recent version ✅
```

### Required Software
- **Node.js 20.16.0+** and **npm 10.9.0+**
- **Python 3.12+**
- **Angular CLI 19.2.8+**
- **Git**
- **PowerShell 7+** (recommended)

### Install Angular CLI
```powershell
npm install -g @angular/cli@19  ❌
ng version  # Verify installation ✅
```

## Project Initialization

### 1. Create Root Directory
```powershell
# Create and navigate to project root
New-Item -ItemType Directory -Name "Any2PDF" ❌
Set-Location Any2PDF ❌

# Initialize git repository
git init ❌
```

### 2. Create Basic Structure
```powershell
# Create main directories
New-Item -ItemType Directory -Name "backend", "docker", "docs", "scripts", ".github"
New-Item -ItemType Directory -Path ".github\workflows"
```

## Frontend Setup (Angular)

### 1. Generate Angular Application
```powershell
# Create Angular application with specific configuration
ng new frontend --standalone --style=css ✅
Set-Location frontend ✅
```

### 2. Install Dependencies
```powershell
cd frontend
# Install Angular Material
ng add @angular/material --theme=indigo-pink --typography=true --animations=true ✅

# Install Tailwind CSS
npm install -D tailwindcss postcss autoprefixer 🔄
npx tailwindcss init -p 🔄

# Install additional dependencies ✅
npm install @angular/cdk --legacy-peer-deps  
npm install rxjs --legacy-peer-deps 
```

### 3. Configure Tailwind CSS 🔄
```powershell
# Update tailwind.config.js
@"
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
"@ | Out-File -FilePath "tailwind.config.js" -Encoding UTF8

# Update styles.css
@"
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Angular Material theme imports */
@import '@angular/material/prebuilt-themes/indigo-pink.css';
"@ | Out-File -FilePath "src\styles.css" -Encoding UTF8
```

### 4. Create Component Structure
```powershell
# Navigate to src/app
Set-Location src\app

# Create components directory and components ✅
ng generate component components/file-upload --skip-tests=false
ng generate component components/conversion-panel --skip-tests=false
ng generate component components/progress-bar --skip-tests=false
ng generate component components/download-manager --skip-tests=false

# Create services ✅
ng generate service services/api --skip-tests=false
ng generate service services/file --skip-tests=false
ng generate service services/conversion --skip-tests=false

# Create models using Angular CLI ✅
ng generate interface models/file
ng generate interface models/conversion
ng generate interface models/api-response

# Create pages
ng generate component pages/home --skip-tests=false
ng generate component pages/convert --skip-tests=false
ng generate component pages/about --skip-tests=false

# Create guards, interceptors, and pipes using Angular CLI
ng generate guard guards/auth --skip-tests=false 
ng generate interceptor interceptors/api --skip-tests=false 
ng generate pipe pipes/file-size --skip-tests=false 
```

### 5. Create Assets Structure
```powershell
# Create assets subdirectories
Set-Location ..\assets
New-Item -ItemType Directory -Name "images", "icons", "styles"

# Create placeholder files
New-Item -ItemType File -Path "images\.gitkeep"
New-Item -ItemType File -Path "icons\.gitkeep"
New-Item -ItemType File -Path "styles\.gitkeep"
```

### 6. Configure Environment Files
```powershell
Set-Location ..\environments

# Update environment.ts
@"
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8000',
  maxFileSize: 52428800, // 50MB
};
"@ | Out-File -FilePath "environment.ts" -Encoding UTF8

# Update environment.prod.ts
@"
export const environment = {
  production: true,
  apiUrl: '/api',
  maxFileSize: 52428800, // 50MB
};
"@ | Out-File -FilePath "environment.prod.ts" -Encoding UTF8
```

### 7. Return to Project Root
```powershell
# Navigate back to project root
Set-Location ..\..\..\
```

## Backend Setup (FastAPI)

### 1. Create Backend Structure
```powershell
# Navigate to backend directory
Set-Location backend

# Create Python virtual environment
python -m venv venv

# Activate virtual environment
venv\Scripts\Activate.ps1

# Create directory structure
New-Item -ItemType Directory -Path "app\api\endpoints" -Force
New-Item -ItemType Directory -Path "app\core" -Force
New-Item -ItemType Directory -Path "app\services" -Force
New-Item -ItemType Directory -Path "app\models" -Force
New-Item -ItemType Directory -Path "app\schemas" -Force
New-Item -ItemType Directory -Path "app\utils" -Force
New-Item -ItemType Directory -Path "tests\test_api" -Force
New-Item -ItemType Directory -Path "tests\test_services" -Force
New-Item -ItemType Directory -Path "tests\test_utils" -Force
New-Item -ItemType Directory -Path "alembic\versions" -Force
```

### 2. Create Python Files
```powershell
# Create __init__.py files
New-Item -ItemType File -Path "app\__init__.py"
New-Item -ItemType File -Path "app\api\__init__.py"
New-Item -ItemType File -Path "app\api\endpoints\__init__.py"
New-Item -ItemType File -Path "app\core\__init__.py"
New-Item -ItemType File -Path "app\services\__init__.py"
New-Item -ItemType File -Path "app\models\__init__.py"
New-Item -ItemType File -Path "app\schemas\__init__.py"
New-Item -ItemType File -Path "app\utils\__init__.py"
New-Item -ItemType File -Path "tests\__init__.py"
```

### 3. Create Main Application Files
```powershell
# Create main application files
New-Item -ItemType File -Path "app\main.py"

# Create core files
New-Item -ItemType File -Path "app\core\config.py"
New-Item -ItemType File -Path "app\core\database.py"
New-Item -ItemType File -Path "app\core\security.py"

# Create API endpoint files
New-Item -ItemType File -Path "app\api\deps.py"
New-Item -ItemType File -Path "app\api\endpoints\health.py"
New-Item -ItemType File -Path "app\api\endpoints\files.py"
New-Item -ItemType File -Path "app\api\endpoints\conversion.py"

# Create service files
New-Item -ItemType File -Path "app\services\file_service.py"
New-Item -ItemType File -Path "app\services\pdf_service.py"
New-Item -ItemType File -Path "app\services\image_service.py"

# Create model files
New-Item -ItemType File -Path "app\models\file.py"
New-Item -ItemType File -Path "app\models\conversion.py"
New-Item -ItemType File -Path "app\models\user.py"

# Create schema files
New-Item -ItemType File -Path "app\schemas\conversion.py"

# Create utility files
New-Item -ItemType File -Path "app\utils\file_utils.py"
New-Item -ItemType File -Path "app\utils\validation.py"
```

### 4. Create Configuration Files
```powershell
# Create requirements.txt
New-Item -ItemType File -Path "requirements.txt"

# Create environment template
New-Item -ItemType File -Path ".env.example"

# Create actual .env file
Copy-Item ".env.example" ".env"

# Create test configuration
New-Item -ItemType File -Path "tests\conftest.py"

# Create Alembic configuration
New-Item -ItemType File -Path "alembic\env.py"
New-Item -ItemType File -Path "alembic\alembic.ini"
```

### 5. Return to Project Root
```powershell
# Navigate back to project root
Set-Location ..
```

## Documentation and Configuration

### 1. Create Configuration Files
```powershell
# Create Docker configuration
Set-Location docker
New-Item -ItemType File -Path "docker-compose.yml"
New-Item -ItemType File -Path "docker-compose.prod.yml"
New-Item -ItemType Directory -Name "nginx"
New-Item -ItemType File -Path "nginx\nginx.conf"

# Create utility scripts
Set-Location ..\scripts
New-Item -ItemType File -Path "setup.ps1"
New-Item -ItemType File -Path "deploy.ps1"
New-Item -ItemType File -Path "backup.ps1"

# Create GitHub workflows
Set-Location "..\.github\workflows"
New-Item -ItemType File -Path "ci.yml"
New-Item -ItemType File -Path "deploy.yml"

# Create root configuration files
Set-Location ..\..
New-Item -ItemType File -Path ".gitignore"
New-Item -ItemType File -Path "docker-compose.yml"
```

## Verification

### 1. Verify Project Structure
```powershell
# Check if all main directories exist
Write-Host "Verifying project structure..."

if (Test-Path "frontend\src\app\components") { Write-Host "✓ Frontend components directory exists" }
if (Test-Path "frontend\src\app\services") { Write-Host "✓ Frontend services directory exists" }
if (Test-Path "frontend\src\app\models") { Write-Host "✓ Frontend models directory exists" }
if (Test-Path "frontend\src\app\pages") { Write-Host "✓ Frontend pages directory exists" }

if (Test-Path "backend\app\api\endpoints") { Write-Host "✓ Backend API endpoints directory exists" }
if (Test-Path "backend\app\core") { Write-Host "✓ Backend core directory exists" }
if (Test-Path "backend\app\services") { Write-Host "✓ Backend services directory exists" }
if (Test-Path "backend\app\models") { Write-Host "✓ Backend models directory exists" }

if (Test-Path "docs\README.md") { Write-Host "✓ Documentation index exists" }
if (Test-Path "docs\ARCHITECTURE.md") { Write-Host "✓ Architecture documentation exists" }
if (Test-Path "docs\SETUP.md") { Write-Host "✓ Setup guide exists" }

Write-Host "Project structure verification complete!"
```

### 2. Install Dependencies
```powershell
# Install backend dependencies
Set-Location backend
venv\Scripts\Activate.ps1
pip install fastapi uvicorn python-multipart pydantic pydantic-settings sqlalchemy alembic reportlab pillow PyPDF2 pytest

# Install frontend dependencies
Set-Location ..\frontend
npm install

# Return to root
Set-Location ..
```

## Next Steps

After completing this setup:

### 1. Start Development Servers
```powershell
# Terminal 1 - Backend
Set-Location backend
venv\Scripts\Activate.ps1
uvicorn app.main:app --reload

# Terminal 2 - Frontend (new PowerShell window)
Set-Location frontend
ng serve
```

### 2. Access Applications
- **Frontend**: http://localhost:4200
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

### 3. Begin Development
- Implement file upload functionality in Angular components
- Add conversion logic in FastAPI services
- Create user interface components
- Write tests for both frontend and backend

**Note**: The project structure is now complete. The next step is to implement the actual code in each file according to your development needs.

---

**Last Updated**: January 2025  
**Maintained by**: Any2PDF Development Team
