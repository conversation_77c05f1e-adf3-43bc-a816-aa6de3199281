import { Injectable } from '@angular/core';
import { HttpClient, HttpEvent, HttpEventType, HttpRequest } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { UploadResponse, FileMetadata, UploadProgress } from '../models/upload.models';

@Injectable({
  providedIn: 'root'
})
export class UploadService {
  private uploadProgressSubject = new BehaviorSubject<UploadProgress>({ progress: 0, status: 'idle' });
  public uploadProgress$ = this.uploadProgressSubject.asObservable();

  constructor(private http: HttpClient) {}

  uploadFiles(files: FileList): Observable<UploadResponse> {
    const formData = new FormData();
    
    // Add files to FormData
    for (let i = 0; i < files.length; i++) {
      formData.append('files', files[i]);
    }

    // Create HTTP request with progress tracking
    const request = new HttpRequest('POST', `${environment.apiUrl}/upload`, formData, {
      reportProgress: true
    });

    return this.http.request<UploadResponse>(request).pipe(
      map((event: HttpEvent<UploadResponse>) => {
        switch (event.type) {
          case HttpEventType.UploadProgress:
            if (event.total) {
              const progress = Math.round(100 * event.loaded / event.total);
              this.uploadProgressSubject.next({ progress, status: 'uploading' });
            }
            break;
          case HttpEventType.Response:
            this.uploadProgressSubject.next({ progress: 100, status: 'completed' });
            return event.body!;
        }
        return null as any;
      })
    );
  }

  validateFile(file: File): string | null {
    // Check file size
    if (file.size > environment.maxFileSize) {
      return `File size exceeds maximum limit of ${environment.maxFileSize / 1024 / 1024}MB`;
    }

    // Check file type
    const allowedTypes = ['.jpg', '.jpeg', '.png', '.pdf', '.docx', '.txt'];
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
      return `File type ${fileExtension} is not supported`;
    }

    return null;
  }

  resetProgress(): void {
    this.uploadProgressSubject.next({ progress: 0, status: 'idle' });
  }
}